/**
 * نظام إدارة الرسوم البيانية
 * Charts Management System
 */

class ChartsManager {
    constructor() {
        this.charts = {};
        this.chartColors = {
            excellent: '#10b981',
            good: '#f59e0b',
            poor: '#ef4444',
            primary: '#2563eb',
            secondary: '#64748b'
        };
    }

    /**
     * تهيئة جميع الرسوم البيانية
     * Initialize all charts
     */
    initializeCharts() {
        this.createAchievementChart();
        this.createEvaluationChart();
        this.createDepartmentChart();
        this.createTrendChart();
    }

    /**
     * رسم بياني للإنجاز مقابل الخطة
     * Achievement vs Plan Chart
     */
    createAchievementChart() {
        const ctx = document.getElementById('achievementChart');
        if (!ctx) return;

        const data = this.getAchievementData();
        
        if (this.charts.achievement) {
            this.charts.achievement.destroy();
        }

        this.charts.achievement = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'المستهدف',
                        data: data.targets,
                        backgroundColor: this.chartColors.secondary,
                        borderColor: this.chartColors.secondary,
                        borderWidth: 1
                    },
                    {
                        label: 'الفعلي',
                        data: data.actuals,
                        backgroundColor: this.chartColors.primary,
                        borderColor: this.chartColors.primary,
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        rtl: true,
                        textDirection: 'rtl'
                    },
                    tooltip: {
                        rtl: true,
                        textDirection: 'rtl',
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${Utils.formatNumber(context.parsed.y)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatNumber(value);
                            }
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    }

    /**
     * رسم بياني لتوزيع التقييمات
     * Evaluation Distribution Chart
     */
    createEvaluationChart() {
        const ctx = document.getElementById('evaluationChart');
        if (!ctx) return;

        const data = this.getEvaluationData();
        
        if (this.charts.evaluation) {
            this.charts.evaluation.destroy();
        }

        this.charts.evaluation = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['ممتاز (≥90%)', 'جيد (70-89%)', 'يحتاج تحسين (<70%)'],
                datasets: [{
                    data: [data.excellent, data.good, data.poor],
                    backgroundColor: [
                        this.chartColors.excellent,
                        this.chartColors.good,
                        this.chartColors.poor
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        rtl: true,
                        textDirection: 'rtl'
                    },
                    tooltip: {
                        rtl: true,
                        textDirection: 'rtl',
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * رسم بياني للأقسام
     * Department Chart
     */
    createDepartmentChart() {
        const ctx = document.getElementById('departmentChart');
        if (!ctx) return;

        const data = this.getDepartmentData();
        
        if (this.charts.department) {
            this.charts.department.destroy();
        }

        this.charts.department = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'متوسط الأداء',
                    data: data.averages,
                    backgroundColor: 'rgba(37, 99, 235, 0.2)',
                    borderColor: this.chartColors.primary,
                    borderWidth: 2,
                    pointBackgroundColor: this.chartColors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        rtl: true,
                        textDirection: 'rtl'
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20,
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * رسم بياني للاتجاهات
     * Trend Chart
     */
    createTrendChart() {
        const ctx = document.getElementById('trendChart');
        if (!ctx) return;

        const data = this.getTrendData();
        
        if (this.charts.trend) {
            this.charts.trend.destroy();
        }

        this.charts.trend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'متوسط الأداء',
                    data: data.values,
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderColor: this.chartColors.primary,
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: this.chartColors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        rtl: true,
                        textDirection: 'rtl'
                    },
                    tooltip: {
                        rtl: true,
                        textDirection: 'rtl'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * الحصول على بيانات الإنجاز
     * Get achievement data
     */
    getAchievementData() {
        const kpis = window.dataManager.getFilteredKPIs();
        
        return {
            labels: kpis.map(kpi => kpi.name),
            targets: kpis.map(kpi => kpi.target),
            actuals: kpis.map(kpi => kpi.actual)
        };
    }

    /**
     * الحصول على بيانات التقييم
     * Get evaluation data
     */
    getEvaluationData() {
        const stats = window.dataManager.getKPIStatistics();
        
        return {
            excellent: stats.excellent,
            good: stats.good,
            poor: stats.poor
        };
    }

    /**
     * الحصول على بيانات الأقسام
     * Get department data
     */
    getDepartmentData() {
        const departmentData = window.dataManager.getDataByDepartment();
        
        return {
            labels: Object.values(departmentData).map(dept => dept.name),
            averages: Object.values(departmentData).map(dept => 
                Math.round(dept.stats.averagePercentage || 0)
            )
        };
    }

    /**
     * الحصول على بيانات الاتجاهات
     * Get trend data
     */
    getTrendData() {
        const periodData = window.dataManager.getDataByPeriod();
        
        return {
            labels: Object.values(periodData).map(period => period.name),
            values: Object.values(periodData).map(period => 
                Math.round(period.stats.averagePercentage || 0)
            )
        };
    }

    /**
     * تحديث جميع الرسوم البيانية
     * Update all charts
     */
    updateCharts() {
        this.updateAchievementChart();
        this.updateEvaluationChart();
        this.updateDepartmentChart();
        this.updateTrendChart();
    }

    /**
     * تحديث رسم الإنجاز
     * Update achievement chart
     */
    updateAchievementChart() {
        if (!this.charts.achievement) return;

        const data = this.getAchievementData();
        this.charts.achievement.data.labels = data.labels;
        this.charts.achievement.data.datasets[0].data = data.targets;
        this.charts.achievement.data.datasets[1].data = data.actuals;
        this.charts.achievement.update();
    }

    /**
     * تحديث رسم التقييم
     * Update evaluation chart
     */
    updateEvaluationChart() {
        if (!this.charts.evaluation) return;

        const data = this.getEvaluationData();
        this.charts.evaluation.data.datasets[0].data = [data.excellent, data.good, data.poor];
        this.charts.evaluation.update();
    }

    /**
     * تحديث رسم الأقسام
     * Update department chart
     */
    updateDepartmentChart() {
        if (!this.charts.department) return;

        const data = this.getDepartmentData();
        this.charts.department.data.labels = data.labels;
        this.charts.department.data.datasets[0].data = data.averages;
        this.charts.department.update();
    }

    /**
     * تحديث رسم الاتجاهات
     * Update trend chart
     */
    updateTrendChart() {
        if (!this.charts.trend) return;

        const data = this.getTrendData();
        this.charts.trend.data.labels = data.labels;
        this.charts.trend.data.datasets[0].data = data.values;
        this.charts.trend.update();
    }

    /**
     * تدمير جميع الرسوم البيانية
     * Destroy all charts
     */
    destroyCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }

    /**
     * إنشاء رسم بياني مخصص
     * Create custom chart
     */
    createCustomChart(canvasId, type, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    rtl: true,
                    textDirection: 'rtl'
                },
                tooltip: {
                    rtl: true,
                    textDirection: 'rtl'
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, {
            type,
            data,
            options: Utils.mergeObjects(defaultOptions, options)
        });

        return this.charts[canvasId];
    }
}

    /**
     * إنشاء رسم بياني للمقاييس (Gauge)
     * Create gauge chart
     */
    createGaugeChart(canvasId, value, max = 100, title = '') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const percentage = (value / max) * 100;
        const color = percentage >= 90 ? this.chartColors.excellent :
                     percentage >= 70 ? this.chartColors.good : this.chartColors.poor;

        this.charts[canvasId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [value, max - value],
                    backgroundColor: [color, '#e5e7eb'],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                elements: {
                    arc: {
                        borderWidth: 0
                    }
                }
            },
            plugins: [{
                id: 'gaugeText',
                beforeDraw: (chart) => {
                    const { ctx, chartArea } = chart;
                    const centerX = (chartArea.left + chartArea.right) / 2;
                    const centerY = (chartArea.top + chartArea.bottom) / 2;

                    ctx.save();
                    ctx.font = 'bold 24px Cairo';
                    ctx.fillStyle = color;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(`${percentage.toFixed(1)}%`, centerX, centerY);

                    if (title) {
                        ctx.font = '14px Cairo';
                        ctx.fillStyle = '#64748b';
                        ctx.fillText(title, centerX, centerY + 30);
                    }
                    ctx.restore();
                }
            }]
        });

        return this.charts[canvasId];
    }

    /**
     * تحديث رسم المقياس
     * Update gauge chart
     */
    updateGaugeChart(canvasId, value, max = 100) {
        const chart = this.charts[canvasId];
        if (!chart) return;

        const percentage = (value / max) * 100;
        const color = percentage >= 90 ? this.chartColors.excellent :
                     percentage >= 70 ? this.chartColors.good : this.chartColors.poor;

        chart.data.datasets[0].data = [value, max - value];
        chart.data.datasets[0].backgroundColor = [color, '#e5e7eb'];
        chart.update();
    }

    /**
     * إنشاء رسم بياني للمقارنة
     * Create comparison chart
     */
    createComparisonChart(canvasId, data, title = '') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'المستهدف',
                        data: data.targets,
                        backgroundColor: 'rgba(100, 116, 139, 0.7)',
                        borderColor: this.chartColors.secondary,
                        borderWidth: 1
                    },
                    {
                        label: 'الفعلي',
                        data: data.actuals,
                        backgroundColor: data.actuals.map(actual => {
                            const target = data.targets[data.actuals.indexOf(actual)];
                            const percentage = (actual / target) * 100;
                            return percentage >= 90 ? 'rgba(16, 185, 129, 0.7)' :
                                   percentage >= 70 ? 'rgba(245, 158, 11, 0.7)' : 'rgba(239, 68, 68, 0.7)';
                        }),
                        borderColor: data.actuals.map(actual => {
                            const target = data.targets[data.actuals.indexOf(actual)];
                            const percentage = (actual / target) * 100;
                            return percentage >= 90 ? this.chartColors.excellent :
                                   percentage >= 70 ? this.chartColors.good : this.chartColors.poor;
                        }),
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: !!title,
                        text: title,
                        font: {
                            family: 'Cairo',
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'top',
                        rtl: true,
                        labels: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    },
                    tooltip: {
                        rtl: true,
                        titleFont: {
                            family: 'Cairo'
                        },
                        bodyFont: {
                            family: 'Cairo'
                        },
                        callbacks: {
                            label: function(context) {
                                const value = Utils.formatNumber(context.parsed.y);
                                return `${context.dataset.label}: ${value}`;
                            },
                            afterLabel: function(context) {
                                if (context.datasetIndex === 1) {
                                    const target = context.chart.data.datasets[0].data[context.dataIndex];
                                    const actual = context.parsed.y;
                                    const percentage = ((actual / target) * 100).toFixed(1);
                                    return `النسبة: ${percentage}%`;
                                }
                                return '';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Cairo'
                            },
                            callback: function(value) {
                                return Utils.formatNumber(value);
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Cairo'
                            },
                            maxRotation: 45
                        }
                    }
                }
            }
        });

        return this.charts[canvasId];
    }

    /**
     * تحديث جميع الرسوم البيانية مع البيانات الجديدة
     * Update all charts with new data
     */
    refreshAllCharts(filters = {}) {
        // تدمير الرسوم الحالية
        this.destroyCharts();

        // إعادة إنشاء الرسوم مع البيانات الجديدة
        setTimeout(() => {
            this.initializeCharts();
        }, 100);
    }

    /**
     * إنشاء رسم بياني مخصص
     * Create custom chart
     */
    createCustomChart(canvasId, type, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    rtl: true,
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                tooltip: {
                    rtl: true,
                    titleFont: {
                        family: 'Cairo'
                    },
                    bodyFont: {
                        family: 'Cairo'
                    }
                }
            },
            scales: type !== 'pie' && type !== 'doughnut' ? {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            } : {}
        };

        const mergedOptions = { ...defaultOptions, ...options };

        this.charts[canvasId] = new Chart(ctx, {
            type: type,
            data: data,
            options: mergedOptions
        });

        return this.charts[canvasId];
    }

    /**
     * تصدير الرسم البياني كصورة
     * Export chart as image
     */
    exportChartAsImage(chartId, fileName = 'chart') {
        const canvas = document.getElementById(chartId);
        if (!canvas) return;

        const link = document.createElement('a');
        link.download = `${fileName}.png`;
        link.href = canvas.toDataURL();
        link.click();
    }
}

// إنشاء مثيل عام لمدير الرسوم البيانية
window.chartsManager = new ChartsManager();
