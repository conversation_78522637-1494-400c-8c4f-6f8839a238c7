/**
 * نظام التصدير
 * Export System
 */

class ExportManager {
    constructor() {
        this.exportFormats = {
            PDF: 'pdf',
            EXCEL: 'excel',
            JSON: 'json'
        };
    }

    /**
     * تصدير لوحة المؤشرات كـ PDF
     * Export dashboard as PDF
     */
    async exportDashboardToPDF() {
        try {
            Utils.showLoading();
            
            // إخفاء العناصر غير المرغوب فيها في التصدير
            this.hideElementsForExport();
            
            // التقاط لقطة شاشة للوحة
            const canvas = await html2canvas(document.getElementById('dashboard'), {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 1200,
                height: window.innerHeight
            });
            
            // إنشاء PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');
            
            // إضافة العنوان
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(16);
            pdf.text('KPI Dashboard Report', 105, 20, { align: 'center' });
            
            // إضافة التاريخ
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            pdf.text(`Generated on: ${new Date().toLocaleDateString('ar-SA')}`, 105, 30, { align: 'center' });
            
            // إضافة الصورة
            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 190;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            
            pdf.addImage(imgData, 'PNG', 10, 40, imgWidth, imgHeight);
            
            // إضافة معلومات إضافية
            const stats = window.dataManager.getKPIStatistics();
            let yPosition = 40 + imgHeight + 20;
            
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(12);
            pdf.text('Summary Statistics:', 10, yPosition);
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            yPosition += 10;
            pdf.text(`Total KPIs: ${stats.total}`, 10, yPosition);
            yPosition += 7;
            pdf.text(`Excellent Performance: ${stats.excellent}`, 10, yPosition);
            yPosition += 7;
            pdf.text(`Good Performance: ${stats.good}`, 10, yPosition);
            yPosition += 7;
            pdf.text(`Needs Improvement: ${stats.poor}`, 10, yPosition);
            yPosition += 7;
            pdf.text(`Average Performance: ${Utils.formatNumber(stats.averagePercentage)}%`, 10, yPosition);
            
            // حفظ الملف
            const fileName = `KPI_Dashboard_${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);
            
            // إظهار العناصر المخفية
            this.showElementsAfterExport();
            Utils.hideLoading();
            
            Utils.showToast('تم تصدير التقرير بنجاح', 'success');
            
        } catch (error) {
            console.error('PDF export error:', error);
            this.showElementsAfterExport();
            Utils.hideLoading();
            Utils.showToast('فشل في تصدير التقرير', 'error');
        }
    }

    /**
     * تصدير الجدول كـ Excel
     * Export table as Excel
     */
    async exportTableToExcel(filters = {}) {
        try {
            if (!window.authManager.hasPermission('export_data')) {
                Utils.showToast('ليس لديك صلاحية لتصدير البيانات', 'error');
                return;
            }

            Utils.showLoading();
            
            const kpis = window.dataManager.getFilteredKPIs(filters);
            const excelData = Utils.convertToExcelFormat(kpis);
            
            // إنشاء workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(excelData);
            
            // تنسيق العرض
            const colWidths = [
                { wch: 25 }, // المؤشر
                { wch: 40 }, // التعريف
                { wch: 12 }, // المستهدف
                { wch: 12 }, // الفعلي
                { wch: 10 }, // النسبة
                { wch: 10 }, // التقييم
                { wch: 30 }, // الملاحظات
                { wch: 15 }, // الفترة
                { wch: 15 }  // القسم
            ];
            ws['!cols'] = colWidths;
            
            // إضافة الورقة إلى الكتاب
            XLSX.utils.book_append_sheet(wb, ws, 'KPI Data');
            
            // إضافة ورقة الإحصائيات
            const stats = window.dataManager.getKPIStatistics(filters);
            const statsData = [
                { 'المؤشر': 'إجمالي المؤشرات', 'القيمة': stats.total },
                { 'المؤشر': 'أداء ممتاز', 'القيمة': stats.excellent },
                { 'المؤشر': 'أداء جيد', 'القيمة': stats.good },
                { 'المؤشر': 'يحتاج تحسين', 'القيمة': stats.poor },
                { 'المؤشر': 'متوسط الأداء', 'القيمة': `${Utils.formatNumber(stats.averagePercentage)}%` }
            ];
            
            const statsWs = XLSX.utils.json_to_sheet(statsData);
            statsWs['!cols'] = [{ wch: 20 }, { wch: 15 }];
            XLSX.utils.book_append_sheet(wb, statsWs, 'Statistics');
            
            // حفظ الملف
            const fileName = `KPI_Data_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
            
            Utils.hideLoading();
            Utils.showToast('تم تصدير البيانات بنجاح', 'success');
            
        } catch (error) {
            console.error('Excel export error:', error);
            Utils.hideLoading();
            Utils.showToast('فشل في تصدير البيانات', 'error');
        }
    }

    /**
     * تصدير بيانات JSON
     * Export JSON data
     */
    exportToJSON(filters = {}) {
        try {
            const kpis = window.dataManager.getFilteredKPIs(filters);
            const stats = window.dataManager.getKPIStatistics(filters);
            
            const exportData = {
                metadata: {
                    exportDate: new Date().toISOString(),
                    totalRecords: kpis.length,
                    filters: filters,
                    generatedBy: window.authManager.getCurrentUser()?.name || 'Unknown'
                },
                statistics: stats,
                kpis: kpis
            };
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `KPI_Data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            Utils.showToast('تم تصدير البيانات بنجاح', 'success');
            
        } catch (error) {
            console.error('JSON export error:', error);
            Utils.showToast('فشل في تصدير البيانات', 'error');
        }
    }

    /**
     * إخفاء العناصر أثناء التصدير
     * Hide elements during export
     */
    hideElementsForExport() {
        const elementsToHide = [
            '.table-actions',
            '.btn',
            '.nav-tabs',
            '.filters-section button'
        ];
        
        elementsToHide.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.display = 'none';
                el.setAttribute('data-hidden-for-export', 'true');
            });
        });
    }

    /**
     * إظهار العناصر بعد التصدير
     * Show elements after export
     */
    showElementsAfterExport() {
        const hiddenElements = document.querySelectorAll('[data-hidden-for-export]');
        hiddenElements.forEach(el => {
            el.style.display = '';
            el.removeAttribute('data-hidden-for-export');
        });
    }

    /**
     * تصدير تقرير مخصص
     * Export custom report
     */
    async exportCustomReport(reportType, filters = {}) {
        try {
            Utils.showLoading();
            
            switch (reportType) {
                case 'department':
                    await this.exportDepartmentReport(filters);
                    break;
                case 'period':
                    await this.exportPeriodReport(filters);
                    break;
                case 'performance':
                    await this.exportPerformanceReport(filters);
                    break;
                default:
                    throw new Error('نوع التقرير غير مدعوم');
            }
            
        } catch (error) {
            console.error('Custom report export error:', error);
            Utils.hideLoading();
            Utils.showToast('فشل في تصدير التقرير المخصص', 'error');
        }
    }

    /**
     * تصدير تقرير الأقسام
     * Export department report
     */
    async exportDepartmentReport(filters) {
        const departmentData = window.dataManager.getDataByDepartment(filters);
        const wb = XLSX.utils.book_new();
        
        Object.keys(departmentData).forEach(deptCode => {
            const dept = departmentData[deptCode];
            const deptKPIs = Utils.convertToExcelFormat(dept.kpis);
            
            const ws = XLSX.utils.json_to_sheet(deptKPIs);
            ws['!cols'] = [
                { wch: 25 }, { wch: 40 }, { wch: 12 }, { wch: 12 },
                { wch: 10 }, { wch: 10 }, { wch: 30 }, { wch: 15 }, { wch: 15 }
            ];
            
            XLSX.utils.book_append_sheet(wb, ws, dept.name);
        });
        
        const fileName = `Department_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);
        
        Utils.hideLoading();
        Utils.showToast('تم تصدير تقرير الأقسام بنجاح', 'success');
    }

    /**
     * تصدير تقرير الفترات
     * Export period report
     */
    async exportPeriodReport(filters) {
        const periodData = window.dataManager.getDataByPeriod(filters);
        const wb = XLSX.utils.book_new();
        
        Object.keys(periodData).forEach(periodCode => {
            const period = periodData[periodCode];
            const periodKPIs = Utils.convertToExcelFormat(period.kpis);
            
            const ws = XLSX.utils.json_to_sheet(periodKPIs);
            ws['!cols'] = [
                { wch: 25 }, { wch: 40 }, { wch: 12 }, { wch: 12 },
                { wch: 10 }, { wch: 10 }, { wch: 30 }, { wch: 15 }, { wch: 15 }
            ];
            
            XLSX.utils.book_append_sheet(wb, ws, period.name);
        });
        
        const fileName = `Period_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);
        
        Utils.hideLoading();
        Utils.showToast('تم تصدير تقرير الفترات بنجاح', 'success');
    }

    /**
     * تصدير تقرير الأداء
     * Export performance report
     */
    async exportPerformanceReport(filters) {
        const kpis = window.dataManager.getFilteredKPIs(filters);
        const stats = window.dataManager.getKPIStatistics(filters);
        
        const wb = XLSX.utils.book_new();
        
        // ورقة البيانات الرئيسية
        const mainData = Utils.convertToExcelFormat(kpis);
        const mainWs = XLSX.utils.json_to_sheet(mainData);
        mainWs['!cols'] = [
            { wch: 25 }, { wch: 40 }, { wch: 12 }, { wch: 12 },
            { wch: 10 }, { wch: 10 }, { wch: 30 }, { wch: 15 }, { wch: 15 }
        ];
        XLSX.utils.book_append_sheet(wb, mainWs, 'KPI Data');
        
        // ورقة الإحصائيات
        const statsData = [
            { 'المؤشر': 'إجمالي المؤشرات', 'القيمة': stats.total },
            { 'المؤشر': 'أداء ممتاز (≥90%)', 'القيمة': stats.excellent },
            { 'المؤشر': 'أداء جيد (70-89%)', 'القيمة': stats.good },
            { 'المؤشر': 'يحتاج تحسين (<70%)', 'القيمة': stats.poor },
            { 'المؤشر': 'متوسط الأداء', 'القيمة': `${Utils.formatNumber(stats.averagePercentage)}%` },
            { 'المؤشر': 'إجمالي المستهدف', 'القيمة': Utils.formatNumber(stats.totalTarget) },
            { 'المؤشر': 'إجمالي الفعلي', 'القيمة': Utils.formatNumber(stats.totalActual) }
        ];
        
        const statsWs = XLSX.utils.json_to_sheet(statsData);
        statsWs['!cols'] = [{ wch: 25 }, { wch: 20 }];
        XLSX.utils.book_append_sheet(wb, statsWs, 'Statistics');
        
        // ورقة الأداء حسب القسم
        const deptData = window.dataManager.getDataByDepartment(filters);
        const deptSummary = Object.keys(deptData).map(deptCode => ({
            'القسم': deptData[deptCode].name,
            'عدد المؤشرات': deptData[deptCode].stats.total,
            'متوسط الأداء': `${Utils.formatNumber(deptData[deptCode].stats.averagePercentage)}%`,
            'ممتاز': deptData[deptCode].stats.excellent,
            'جيد': deptData[deptCode].stats.good,
            'يحتاج تحسين': deptData[deptCode].stats.poor
        }));
        
        const deptWs = XLSX.utils.json_to_sheet(deptSummary);
        deptWs['!cols'] = [
            { wch: 20 }, { wch: 15 }, { wch: 15 },
            { wch: 10 }, { wch: 10 }, { wch: 15 }
        ];
        XLSX.utils.book_append_sheet(wb, deptWs, 'Department Summary');
        
        const fileName = `Performance_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);
        
        Utils.hideLoading();
        Utils.showToast('تم تصدير تقرير الأداء بنجاح', 'success');
    }

    /**
     * تصدير بيانات مخصصة
     * Export custom data
     */
    exportCustomData(data, fileName, format = 'excel') {
        try {
            switch (format) {
                case 'excel':
                    this.exportArrayToExcel(data, fileName);
                    break;
                case 'json':
                    this.exportArrayToJSON(data, fileName);
                    break;
                case 'csv':
                    this.exportArrayToCSV(data, fileName);
                    break;
                default:
                    throw new Error('تنسيق التصدير غير مدعوم');
            }
        } catch (error) {
            console.error('Custom export error:', error);
            Utils.showToast('فشل في تصدير البيانات المخصصة', 'error');
        }
    }

    /**
     * تصدير مصفوفة إلى Excel
     * Export array to Excel
     */
    exportArrayToExcel(data, fileName) {
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(data);
        
        // تحديد عرض الأعمدة تلقائياً
        const colWidths = Object.keys(data[0] || {}).map(key => ({
            wch: Math.max(key.length, 15)
        }));
        ws['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(wb, ws, 'Data');
        XLSX.writeFile(wb, `${fileName}.xlsx`);
        
        Utils.showToast('تم التصدير بنجاح', 'success');
    }

    /**
     * تصدير مصفوفة إلى JSON
     * Export array to JSON
     */
    exportArrayToJSON(data, fileName) {
        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${fileName}.json`;
        link.click();
        
        Utils.showToast('تم التصدير بنجاح', 'success');
    }

    /**
     * تصدير مصفوفة إلى CSV
     * Export array to CSV
     */
    exportArrayToCSV(data, fileName) {
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(data);
        
        XLSX.utils.book_append_sheet(wb, ws, 'Data');
        XLSX.writeFile(wb, `${fileName}.csv`, { bookType: 'csv' });
        
        Utils.showToast('تم التصدير بنجاح', 'success');
    }

    /**
     * إنشاء تقرير PDF مخصص
     * Create custom PDF report
     */
    async createCustomPDFReport(title, content, charts = []) {
        try {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');
            
            // إضافة العنوان
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(18);
            pdf.text(title, 105, 20, { align: 'center' });
            
            // إضافة التاريخ
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            pdf.text(`تاريخ الإنشاء: ${Utils.formatDate(new Date())}`, 105, 30, { align: 'center' });
            
            let yPosition = 50;
            
            // إضافة المحتوى النصي
            if (content && content.length > 0) {
                pdf.setFont('helvetica', 'normal');
                pdf.setFontSize(12);
                
                content.forEach(section => {
                    if (yPosition > 250) {
                        pdf.addPage();
                        yPosition = 20;
                    }
                    
                    pdf.setFont('helvetica', 'bold');
                    pdf.text(section.title, 10, yPosition);
                    yPosition += 10;
                    
                    pdf.setFont('helvetica', 'normal');
                    const lines = pdf.splitTextToSize(section.content, 190);
                    pdf.text(lines, 10, yPosition);
                    yPosition += lines.length * 7 + 10;
                });
            }
            
            // إضافة الرسوم البيانية
            for (const chartId of charts) {
                const canvas = document.getElementById(chartId);
                if (canvas) {
                    if (yPosition > 200) {
                        pdf.addPage();
                        yPosition = 20;
                    }
                    
                    const chartImage = canvas.toDataURL('image/png');
                    pdf.addImage(chartImage, 'PNG', 10, yPosition, 190, 100);
                    yPosition += 110;
                }
            }
            
            return pdf;
            
        } catch (error) {
            console.error('Custom PDF creation error:', error);
            throw error;
        }
    }

    /**
     * طباعة التقرير
     * Print report
     */
    printReport() {
        try {
            // إخفاء العناصر غير المرغوب فيها
            this.hideElementsForExport();
            
            // طباعة الصفحة
            window.print();
            
            // إظهار العناصر بعد الطباعة
            setTimeout(() => {
                this.showElementsAfterExport();
            }, 1000);
            
        } catch (error) {
            console.error('Print error:', error);
            this.showElementsAfterExport();
            Utils.showToast('فشل في طباعة التقرير', 'error');
        }
    }
}

// إنشاء مثيل عام لمدير التصدير
window.exportManager = new ExportManager();
