<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم مؤشرات الأداء الرئيسية</title>
    
    <!-- External Libraries -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> دخول
                </button>
            </form>
            <div class="demo-users">
                <h4>حسابات تجريبية:</h4>
                <p><strong>مدير النظام:</strong> admin / admin123</p>
                <p><strong>مدير القسم:</strong> manager / manager123</p>
                <p><strong>قارئ:</strong> viewer / viewer123</p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="hidden">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <h1>لوحة تحكم مؤشرات الأداء</h1>
                </div>
                <div class="user-info">
                    <span id="userWelcome"></span>
                    <button type="button" id="logoutBtn" class="btn btn-secondary">
                        <i class="fas fa-sign-out-alt"></i> خروج
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Layout with Sidebar -->
        <div class="main-layout">
            <!-- Sidebar Navigation -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <button type="button" class="sidebar-toggle" id="sidebarToggle" title="طي/توسيع القائمة الجانبية" aria-label="طي أو توسيع القائمة الجانبية">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span class="sidebar-title">القائمة الرئيسية</span>
                </div>

                <nav class="sidebar-nav">
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <button type="button" class="nav-link active" data-tab="dashboard" aria-label="الانتقال إلى لوحة المؤشرات">
                                <i class="fas fa-tachometer-alt"></i>
                                <span class="nav-text">لوحة المؤشرات</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button type="button" class="nav-link" data-tab="data-management" aria-label="الانتقال إلى إدارة البيانات">
                                <i class="fas fa-database"></i>
                                <span class="nav-text">إدارة البيانات</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button type="button" class="nav-link" data-tab="analytics" aria-label="الانتقال إلى التحليلات المتقدمة">
                                <i class="fas fa-chart-line"></i>
                                <span class="nav-text">التحليلات المتقدمة</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button type="button" class="nav-link" data-tab="reports" aria-label="الانتقال إلى التقارير">
                                <i class="fas fa-file-alt"></i>
                                <span class="nav-text">التقارير</span>
                            </button>
                        </li>
                        <li class="nav-item admin-only">
                            <button type="button" class="nav-link" data-tab="users" aria-label="الانتقال إلى إدارة المستخدمين">
                                <i class="fas fa-users"></i>
                                <span class="nav-text">إدارة المستخدمين</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button type="button" class="nav-link" data-tab="settings" aria-label="الانتقال إلى الإعدادات">
                                <i class="fas fa-cog"></i>
                                <span class="nav-text">الإعدادات</span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button type="button" class="nav-link" data-tab="settings">
                                <i class="fas fa-cog"></i>
                                <span class="nav-text">الإعدادات</span>
                            </button>
                        </li>
                    </ul>
                </nav>

                <div class="sidebar-footer">
                    <div class="user-profile">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <div class="user-name" id="sidebarUserName">المستخدم</div>
                            <div class="user-role" id="sidebarUserRole">الدور</div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main Content Area -->
            <main class="main-content" id="mainContent">

                <!-- Breadcrumb Navigation -->
                <nav class="breadcrumb" id="breadcrumb">
                    <span class="breadcrumb-item active">لوحة المؤشرات</span>
                </nav>

                <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <!-- Filters -->
            <div class="filters-section">
                <div class="filter-group">
                    <label for="periodFilter">الفترة الزمنية:</label>
                    <select id="periodFilter">
                        <option value="all">جميع الفترات</option>
                        <option value="2024-Q1">الربع الأول 2024</option>
                        <option value="2024-Q2">الربع الثاني 2024</option>
                        <option value="2024-Q3">الربع الثالث 2024</option>
                        <option value="2024-Q4">الربع الرابع 2024</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="departmentFilter">القسم:</label>
                    <select id="departmentFilter">
                        <option value="all">جميع الأقسام</option>
                        <option value="sales">المبيعات</option>
                        <option value="marketing">التسويق</option>
                        <option value="operations">العمليات</option>
                        <option value="hr">الموارد البشرية</option>
                        <option value="finance">المالية</option>
                    </select>
                </div>
                <button type="button" id="refreshDashboard" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
            </div>

            <!-- KPI Cards -->
            <div class="kpi-grid" id="kpiGrid">
                <!-- KPI cards will be dynamically generated -->
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <h3>الإنجاز مقابل الخطة</h3>
                    <canvas id="achievementChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>توزيع المؤشرات حسب التقييم</h3>
                    <canvas id="evaluationChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>الأداء حسب القسم</h3>
                    <canvas id="departmentChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>اتجاه الأداء</h3>
                    <canvas id="trendChart"></canvas>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-section">
                <div class="table-header">
                    <h3>الجدول التحليلي</h3>
                    <div class="table-actions">
                        <input type="text" id="searchTable" placeholder="البحث في الجدول...">
                        <button type="button" id="exportExcel" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button type="button" id="exportPDF" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table id="kpiTable">
                        <thead>
                            <tr>
                                <th>المؤشر</th>
                                <th>المستهدف</th>
                                <th>الفعلي</th>
                                <th>النسبة</th>
                                <th>التقييم</th>
                                <th>الملاحظات</th>
                                <th>الفترة</th>
                                <th>القسم</th>
                                <th class="admin-manager-only">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="kpiTableBody">
                            <!-- Table rows will be dynamically generated -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Data Management Tab -->
        <div id="data-management" class="tab-content">
            <div class="data-management-header">
                <h2>إدارة البيانات</h2>
                <div class="data-actions">
                    <button type="button" id="addKpiBtn" class="btn btn-primary admin-manager-only">
                        <i class="fas fa-plus"></i> إضافة مؤشر جديد
                    </button>
                    <input type="file" id="excelFileInput" accept=".xlsx,.xls" class="hidden">
                    <button type="button" id="importExcelBtn" class="btn btn-info admin-only">
                        <i class="fas fa-file-import"></i> استيراد Excel
                    </button>
                </div>
            </div>
            
            <!-- KPI Form Modal -->
            <div id="kpiModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modalTitle">إضافة مؤشر جديد</h3>
                        <button type="button" class="close-btn" id="closeModal">&times;</button>
                    </div>
                    <form id="kpiForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="kpiName">اسم المؤشر:</label>
                                <input type="text" id="kpiName" required>
                            </div>
                            <div class="form-group">
                                <label for="kpiDefinition">التعريف:</label>
                                <textarea id="kpiDefinition" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="kpiTarget">المستهدف:</label>
                                <input type="number" id="kpiTarget" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="kpiActual">الفعلي:</label>
                                <input type="number" id="kpiActual" step="0.01" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="kpiPeriod">الفترة:</label>
                                <select id="kpiPeriod" required>
                                    <option value="">اختر الفترة</option>
                                    <option value="2024-Q1">الربع الأول 2024</option>
                                    <option value="2024-Q2">الربع الثاني 2024</option>
                                    <option value="2024-Q3">الربع الثالث 2024</option>
                                    <option value="2024-Q4">الربع الرابع 2024</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="kpiDepartment">القسم:</label>
                                <select id="kpiDepartment" required>
                                    <option value="">اختر القسم</option>
                                    <option value="sales">المبيعات</option>
                                    <option value="marketing">التسويق</option>
                                    <option value="operations">العمليات</option>
                                    <option value="hr">الموارد البشرية</option>
                                    <option value="finance">المالية</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="kpiNotes">الملاحظات:</label>
                            <textarea id="kpiNotes" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancelBtn">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-content">
                    <div class="analytics-header">
                        <h2>التحليلات المتقدمة</h2>
                        <div class="analytics-actions">
                            <button type="button" class="btn btn-primary" onclick="app.generateAnalysisReport()">
                                <i class="fas fa-chart-line"></i> تحليل شامل
                            </button>
                            <button type="button" class="btn btn-info" onclick="app.showTrendAnalysis()">
                                <i class="fas fa-trending-up"></i> تحليل الاتجاهات
                            </button>
                        </div>
                    </div>

                    <!-- Performance Overview -->
                    <div class="performance-overview">
                        <div class="overview-cards">
                            <div class="overview-card">
                                <div class="card-icon excellent">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="card-content">
                                    <h3 id="analyticsExcellentCount">0</h3>
                                    <p>أداء ممتاز</p>
                                </div>
                            </div>
                            <div class="overview-card">
                                <div class="card-icon good">
                                    <i class="fas fa-thumbs-up"></i>
                                </div>
                                <div class="card-content">
                                    <h3 id="analyticsGoodCount">0</h3>
                                    <p>أداء جيد</p>
                                </div>
                            </div>
                            <div class="overview-card">
                                <div class="card-icon poor">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="card-content">
                                    <h3 id="analyticsPoorCount">0</h3>
                                    <p>يحتاج تحسين</p>
                                </div>
                            </div>
                            <div class="overview-card">
                                <div class="card-icon total">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="card-content">
                                    <h3 id="analyticsAveragePerformance">0%</h3>
                                    <p>متوسط الأداء</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Charts -->
                    <div class="advanced-charts">
                        <div class="chart-row">
                            <div class="chart-container">
                                <h3>مقارنة الأداء بين الأقسام</h3>
                                <canvas id="departmentComparisonChart"></canvas>
                            </div>
                            <div class="chart-container">
                                <h3>اتجاه الأداء عبر الفترات</h3>
                                <canvas id="performanceTrendChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-row">
                            <div class="chart-container">
                                <h3>توزيع المؤشرات حسب النطاق</h3>
                                <canvas id="rangeDistributionChart"></canvas>
                            </div>
                            <div class="chart-container">
                                <h3>معدل التحسن الشهري</h3>
                                <canvas id="improvementRateChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div id="reports" class="tab-content">
                    <h2>التقارير</h2>
                    <div class="reports-section">
                        <div class="report-card">
                            <h3>تقرير الأداء الشامل</h3>
                            <p>تقرير شامل لجميع مؤشرات الأداء</p>
                            <button type="button" class="btn btn-primary" onclick="window.exportManager.exportDashboardToPDF()">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                        <div class="report-card">
                            <h3>تقرير الأداء حسب القسم</h3>
                            <p>تقرير مفصل لكل قسم على حدة</p>
                            <button type="button" class="btn btn-primary" onclick="window.exportManager.exportCustomReport('department')">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                        <div class="report-card">
                            <h3>تقرير الأداء حسب الفترة</h3>
                            <p>تحليل الأداء عبر الفترات الزمنية</p>
                            <button type="button" class="btn btn-primary" onclick="window.exportManager.exportCustomReport('period')">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                        <div class="report-card">
                            <h3>تقرير الأداء التفصيلي</h3>
                            <p>تقرير شامل مع التحليلات والتوصيات</p>
                            <button type="button" class="btn btn-primary" onclick="window.exportManager.exportCustomReport('performance')">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                        <div class="report-card">
                            <h3>نسخ احتياطي للبيانات</h3>
                            <p>تصدير جميع البيانات كنسخة احتياطية</p>
                            <button type="button" class="btn btn-info" onclick="window.dataManager.backupData()">
                                <i class="fas fa-download"></i> تحميل النسخة الاحتياطية
                            </button>
                        </div>
                        <div class="report-card">
                            <h3>طباعة التقرير</h3>
                            <p>طباعة اللوحة الحالية</p>
                            <button type="button" class="btn btn-secondary" onclick="window.exportManager.printReport()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Users Management Tab -->
        <div id="users" class="tab-content admin-only">
            <h2>إدارة المستخدمين</h2>
            <div class="users-section">
                <button type="button" id="addUserBtn" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                </button>
                <div class="users-table">
                    <table id="usersTable">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>الدور</th>
                                <th>القسم</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be dynamically loaded -->
                        </tbody>
                    </table>
                </div>
            </div>

            </main>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner hidden">
        <div class="spinner"></div>
        <p>جاري التحميل...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/export.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
