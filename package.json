{"name": "kpi-dashboard", "version": "1.0.0", "description": "نظام لوحة تحكم مؤشرات الأداء الرئيسية - KPI Dashboard System", "main": "index.html", "scripts": {"start": "http-server -p 8000 -o", "dev": "http-server -p 8000 -o -c-1", "test": "echo \"افتح test.html في المتصفح لتشغيل الاختبارات\"", "build": "echo \"لا حاجة للبناء - النظام جاهز للتشغيل\"", "serve": "python -m http.server 8000"}, "keywords": ["kpi", "dashboard", "arabic", "rtl", "charts", "excel", "pdf", "performance", "indicators"], "author": "Augment Agent", "license": "MIT", "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "index.html", "browserslist": ["> 1%", "last 2 versions", "not dead"]}