/**
 * ملف التكوين العام للنظام
 * System Configuration File
 */

const CONFIG = {
    // إعدادات التطبيق العامة
    APP: {
        NAME: 'نظام لوحة تحكم مؤشرات الأداء',
        VERSION: '1.0.0',
        AUTHOR: 'Augment Agent',
        LANGUAGE: 'ar',
        DIRECTION: 'rtl'
    },

    // إعدادات المؤشرات
    KPI: {
        // حدود التقييم
        THRESHOLDS: {
            EXCELLENT: 90,
            GOOD: 70,
            POOR: 0
        },
        
        // الحقول المطلوبة
        REQUIRED_FIELDS: ['name', 'target', 'actual', 'period', 'department'],
        
        // الحد الأقصى لطول النص
        MAX_TEXT_LENGTH: {
            NAME: 100,
            DEFINITION: 500,
            NOTES: 1000
        },
        
        // القيم الافتراضية
        DEFAULTS: {
            TARGET: 100,
            ACTUAL: 0,
            PERIOD: '2024-Q4',
            DEPARTMENT: 'operations'
        }
    },

    // إعدادات المستخدمين
    USERS: {
        // أدوار النظام
        ROLES: {
            ADMIN: {
                name: 'مدير النظام',
                permissions: ['view', 'edit', 'delete', 'manage_users', 'import_data', 'export_data']
            },
            MANAGER: {
                name: 'مدير القسم',
                permissions: ['view', 'edit', 'delete', 'export_data']
            },
            VIEWER: {
                name: 'قارئ',
                permissions: ['view']
            }
        },
        
        // إعدادات كلمة المرور
        PASSWORD: {
            MIN_LENGTH: 6,
            REQUIRE_NUMBERS: false,
            REQUIRE_SPECIAL_CHARS: false
        }
    },

    // إعدادات الواجهة
    UI: {
        // ألوان النظام
        COLORS: {
            PRIMARY: '#2563eb',
            SUCCESS: '#10b981',
            WARNING: '#f59e0b',
            DANGER: '#ef4444',
            INFO: '#06b6d4'
        },
        
        // إعدادات الجداول
        TABLE: {
            ROWS_PER_PAGE: 50,
            ENABLE_PAGINATION: false,
            ENABLE_SORTING: true,
            ENABLE_FILTERING: true
        },
        
        // إعدادات الرسوم البيانية
        CHARTS: {
            ANIMATION_DURATION: 1000,
            RESPONSIVE: true,
            MAINTAIN_ASPECT_RATIO: false
        },
        
        // إعدادات الإشعارات
        NOTIFICATIONS: {
            DURATION: 3000,
            POSITION: 'top-left',
            ENABLE_SOUND: false
        }
    },

    // إعدادات التصدير
    EXPORT: {
        // تنسيقات مدعومة
        FORMATS: ['pdf', 'excel', 'json', 'csv'],
        
        // إعدادات PDF
        PDF: {
            FORMAT: 'a4',
            ORIENTATION: 'portrait',
            MARGIN: 10,
            QUALITY: 2
        },
        
        // إعدادات Excel
        EXCEL: {
            SHEET_NAME: 'KPI Data',
            INCLUDE_STATISTICS: true,
            AUTO_COLUMN_WIDTH: true
        }
    },

    // إعدادات التخزين
    STORAGE: {
        // نوع التخزين المفضل
        PREFERRED_TYPE: 'localStorage', // أو 'indexedDB'
        
        // مفاتيح التخزين
        KEYS: {
            USERS: 'kpi_users',
            KPIS: 'kpi_data',
            CURRENT_USER: 'current_user',
            SETTINGS: 'kpi_settings',
            PREFERENCES: 'user_preferences'
        },
        
        // إعدادات النسخ الاحتياطي
        BACKUP: {
            AUTO_BACKUP: true,
            BACKUP_INTERVAL_DAYS: 7,
            MAX_BACKUPS: 5
        }
    },

    // إعدادات الأداء
    PERFORMANCE: {
        // تحديث تلقائي
        AUTO_REFRESH: {
            ENABLED: true,
            INTERVAL_MINUTES: 5
        },
        
        // تحسين الأداء
        OPTIMIZATION: {
            DEBOUNCE_SEARCH: 300,
            THROTTLE_SCROLL: 100,
            LAZY_LOAD_CHARTS: true
        }
    },

    // إعدادات التطوير
    DEVELOPMENT: {
        DEBUG_MODE: false,
        CONSOLE_LOGGING: true,
        PERFORMANCE_MONITORING: false
    },

    // الأقسام المتاحة
    DEPARTMENTS: {
        'sales': 'المبيعات',
        'marketing': 'التسويق',
        'operations': 'العمليات',
        'hr': 'الموارد البشرية',
        'finance': 'المالية'
    },

    // الفترات الزمنية المتاحة
    PERIODS: {
        '2024-Q1': 'الربع الأول 2024',
        '2024-Q2': 'الربع الثاني 2024',
        '2024-Q3': 'الربع الثالث 2024',
        '2024-Q4': 'الربع الرابع 2024',
        '2025-Q1': 'الربع الأول 2025'
    },

    // رسائل النظام
    MESSAGES: {
        SUCCESS: {
            LOGIN: 'تم تسجيل الدخول بنجاح',
            LOGOUT: 'تم تسجيل الخروج بنجاح',
            KPI_ADDED: 'تم إضافة المؤشر بنجاح',
            KPI_UPDATED: 'تم تحديث المؤشر بنجاح',
            KPI_DELETED: 'تم حذف المؤشر بنجاح',
            DATA_IMPORTED: 'تم استيراد البيانات بنجاح',
            DATA_EXPORTED: 'تم تصدير البيانات بنجاح'
        },
        ERROR: {
            LOGIN_FAILED: 'فشل في تسجيل الدخول',
            PERMISSION_DENIED: 'ليس لديك صلاحية لهذا الإجراء',
            VALIDATION_ERROR: 'خطأ في التحقق من البيانات',
            NETWORK_ERROR: 'خطأ في الاتصال',
            UNKNOWN_ERROR: 'حدث خطأ غير متوقع'
        },
        WARNING: {
            UNSAVED_CHANGES: 'لديك تغييرات غير محفوظة',
            DELETE_CONFIRMATION: 'هل أنت متأكد من الحذف؟',
            LOGOUT_CONFIRMATION: 'هل أنت متأكد من تسجيل الخروج؟'
        },
        INFO: {
            LOADING: 'جاري التحميل...',
            PROCESSING: 'جاري المعالجة...',
            NO_DATA: 'لا توجد بيانات للعرض',
            FILTERS_APPLIED: 'تم تطبيق الفلاتر'
        }
    },

    // إعدادات التحقق من صحة البيانات
    VALIDATION: {
        KPI: {
            NAME: {
                MIN_LENGTH: 3,
                MAX_LENGTH: 100,
                REQUIRED: true
            },
            TARGET: {
                MIN_VALUE: 0.01,
                MAX_VALUE: 999999,
                REQUIRED: true
            },
            ACTUAL: {
                MIN_VALUE: 0,
                MAX_VALUE: 999999,
                REQUIRED: true
            }
        },
        USER: {
            USERNAME: {
                MIN_LENGTH: 3,
                MAX_LENGTH: 50,
                PATTERN: /^[a-zA-Z0-9_]+$/,
                REQUIRED: true
            },
            PASSWORD: {
                MIN_LENGTH: 6,
                MAX_LENGTH: 100,
                REQUIRED: true
            }
        }
    },

    // إعدادات الأمان
    SECURITY: {
        SESSION_TIMEOUT_HOURS: 8,
        MAX_LOGIN_ATTEMPTS: 5,
        LOCKOUT_DURATION_MINUTES: 15,
        ENCRYPT_STORAGE: false // في بيئة الإنتاج، يجب تفعيل التشفير
    }
};

// تصدير التكوين للاستخدام العام
window.CONFIG = CONFIG;

// دالة للحصول على قيمة تكوين
window.getConfig = function(path, defaultValue = null) {
    const keys = path.split('.');
    let value = CONFIG;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

// دالة لتحديث التكوين
window.updateConfig = function(path, newValue) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = CONFIG;
    
    for (const key of keys) {
        if (!(key in target)) {
            target[key] = {};
        }
        target = target[key];
    }
    
    target[lastKey] = newValue;
    
    // حفظ التكوين المحدث
    Utils.StorageManager.set('app_config', CONFIG);
};

// تحميل التكوين المحفوظ عند بدء التطبيق
document.addEventListener('DOMContentLoaded', () => {
    const savedConfig = Utils.StorageManager.get('app_config');
    if (savedConfig) {
        Object.assign(CONFIG, savedConfig);
    }
});
