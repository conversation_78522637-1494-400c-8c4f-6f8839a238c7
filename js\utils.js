/**
 * ملف المرافق والوظائف المساعدة
 * Utilities and Helper Functions
 */

// ===== Constants =====
const STORAGE_KEYS = {
    USERS: 'kpi_users',
    KPIS: 'kpi_data',
    CURRENT_USER: 'current_user',
    SETTINGS: 'kpi_settings'
};

const USER_ROLES = {
    ADMIN: 'admin',
    MANAGER: 'manager',
    VIEWER: 'viewer'
};

const KPI_STATUS = {
    EXCELLENT: 'excellent',
    GOOD: 'good',
    POOR: 'poor'
};

// ===== Local Storage Utilities =====
class StorageManager {
    static set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }

    static get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    }

    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }

    static clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('Error clearing localStorage:', error);
            return false;
        }
    }
}

// ===== Utility Functions =====

/**
 * حساب النسبة المئوية
 * Calculate percentage
 */
function calculatePercentage(actual, target) {
    if (target === 0) return 0;
    return Math.round((actual / target) * 100 * 100) / 100;
}

/**
 * تحديد حالة المؤشر بناءً على النسبة
 * Determine KPI status based on percentage
 */
function getKPIStatus(percentage) {
    if (percentage >= 90) return KPI_STATUS.EXCELLENT;
    if (percentage >= 70) return KPI_STATUS.GOOD;
    return KPI_STATUS.POOR;
}

/**
 * الحصول على أيقونة الحالة
 * Get status icon
 */
function getStatusIcon(status) {
    switch (status) {
        case KPI_STATUS.EXCELLENT:
            return '✅';
        case KPI_STATUS.GOOD:
            return '🕑';
        case KPI_STATUS.POOR:
            return '❗';
        default:
            return '❓';
    }
}

/**
 * الحصول على لون الحالة
 * Get status color
 */
function getStatusColor(status) {
    switch (status) {
        case KPI_STATUS.EXCELLENT:
            return 'var(--success-color)';
        case KPI_STATUS.GOOD:
            return 'var(--warning-color)';
        case KPI_STATUS.POOR:
            return 'var(--danger-color)';
        default:
            return 'var(--text-secondary)';
    }
}

/**
 * تنسيق الأرقام
 * Format numbers
 */
function formatNumber(number, decimals = 2) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * تنسيق التاريخ
 * Format date
 */
function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

/**
 * إنشاء معرف فريد
 * Generate unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * عرض إشعار منبثق
 * Show toast notification
 */
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.getElementById('toastContainer');
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <i class="fas fa-${getToastIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    toastContainer.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease forwards';
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 300);
    }, duration);
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'info-circle';
    }
}

/**
 * عرض/إخفاء شاشة التحميل
 * Show/hide loading spinner
 */
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('hidden');
}

/**
 * تطبيق الصلاحيات على العناصر
 * Apply permissions to elements
 */
function applyPermissions(userRole) {
    document.body.className = `role-${userRole}`;
    
    // إخفاء/إظهار العناصر بناءً على الدور
    const adminOnlyElements = document.querySelectorAll('.admin-only');
    const adminManagerElements = document.querySelectorAll('.admin-manager-only');
    
    adminOnlyElements.forEach(element => {
        element.style.display = userRole === USER_ROLES.ADMIN ? '' : 'none';
    });
    
    adminManagerElements.forEach(element => {
        element.style.display = 
            (userRole === USER_ROLES.ADMIN || userRole === USER_ROLES.MANAGER) ? '' : 'none';
    });
}

/**
 * تصفية البيانات
 * Filter data
 */
function filterData(data, filters) {
    return data.filter(item => {
        let matches = true;
        
        if (filters.period && filters.period !== 'all') {
            matches = matches && item.period === filters.period;
        }
        
        if (filters.department && filters.department !== 'all') {
            matches = matches && item.department === filters.department;
        }
        
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            matches = matches && (
                item.name.toLowerCase().includes(searchTerm) ||
                item.definition.toLowerCase().includes(searchTerm) ||
                item.notes.toLowerCase().includes(searchTerm)
            );
        }
        
        return matches;
    });
}

/**
 * التحقق من صحة البيانات
 * Validate data
 */
function validateKPI(kpi) {
    const errors = [];
    
    if (!kpi.name || kpi.name.trim() === '') {
        errors.push('اسم المؤشر مطلوب');
    }
    
    if (!kpi.target || isNaN(kpi.target) || kpi.target <= 0) {
        errors.push('المستهدف يجب أن يكون رقم موجب');
    }
    
    if (!kpi.actual || isNaN(kpi.actual)) {
        errors.push('الفعلي يجب أن يكون رقم صحيح');
    }
    
    if (!kpi.period) {
        errors.push('الفترة الزمنية مطلوبة');
    }
    
    if (!kpi.department) {
        errors.push('القسم مطلوب');
    }
    
    return errors;
}

/**
 * تحويل البيانات لتنسيق Excel
 * Convert data to Excel format
 */
function convertToExcelFormat(data) {
    return data.map(item => ({
        'المؤشر': item.name,
        'التعريف': item.definition,
        'المستهدف': item.target,
        'الفعلي': item.actual,
        'النسبة': `${item.percentage}%`,
        'التقييم': getStatusIcon(item.status),
        'الملاحظات': item.notes,
        'الفترة': item.period,
        'القسم': getDepartmentName(item.department)
    }));
}

/**
 * الحصول على اسم القسم بالعربية
 * Get department name in Arabic
 */
function getDepartmentName(departmentCode) {
    const departments = {
        'sales': 'المبيعات',
        'marketing': 'التسويق',
        'operations': 'العمليات',
        'hr': 'الموارد البشرية',
        'finance': 'المالية'
    };
    return departments[departmentCode] || departmentCode;
}

/**
 * الحصول على اسم الفترة بالعربية
 * Get period name in Arabic
 */
function getPeriodName(periodCode) {
    const periods = {
        '2024-Q1': 'الربع الأول 2024',
        '2024-Q2': 'الربع الثاني 2024',
        '2024-Q3': 'الربع الثالث 2024',
        '2024-Q4': 'الربع الرابع 2024'
    };
    return periods[periodCode] || periodCode;
}

/**
 * تأخير التنفيذ
 * Delay execution
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * تنظيف النص
 * Sanitize text
 */
function sanitizeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * التحقق من صحة البريد الإلكتروني
 * Validate email
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * تحويل الكائن إلى مصفوفة
 * Convert object to array
 */
function objectToArray(obj) {
    return Object.keys(obj).map(key => ({
        key,
        value: obj[key]
    }));
}

/**
 * دمج الكائنات
 * Merge objects
 */
function mergeObjects(...objects) {
    return Object.assign({}, ...objects);
}

// ===== Event Utilities =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== Export utilities =====
window.Utils = {
    StorageManager,
    calculatePercentage,
    getKPIStatus,
    getStatusIcon,
    getStatusColor,
    formatNumber,
    formatDate,
    generateId,
    showToast,
    showLoading,
    hideLoading,
    applyPermissions,
    filterData,
    validateKPI,
    convertToExcelFormat,
    getDepartmentName,
    getPeriodName,
    delay,
    sanitizeText,
    isValidEmail,
    objectToArray,
    mergeObjects,
    debounce,
    throttle,
    STORAGE_KEYS,
    USER_ROLES,
    KPI_STATUS
};
