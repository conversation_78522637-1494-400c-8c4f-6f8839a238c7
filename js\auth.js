/**
 * نظام إدارة المصادقة والمستخدمين
 * Authentication and User Management System
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.initializeDefaultUsers();
    }

    /**
     * تهيئة المستخدمين الافتراضيين
     * Initialize default users
     */
    initializeDefaultUsers() {
        const existingUsers = Utils.StorageManager.get(Utils.STORAGE_KEYS.USERS);
        
        if (!existingUsers || existingUsers.length === 0) {
            const defaultUsers = [
                {
                    id: 'admin-001',
                    username: 'admin',
                    password: 'admin123', // في التطبيق الحقيقي، يجب تشفير كلمات المرور
                    role: Utils.USER_ROLES.ADMIN,
                    name: 'مدير النظام',
                    department: 'all',
                    createdAt: new Date().toISOString(),
                    isActive: true
                },
                {
                    id: 'manager-001',
                    username: 'manager',
                    password: 'manager123',
                    role: Utils.USER_ROLES.MANAGER,
                    name: 'مدي<PERSON> القسم',
                    department: 'sales',
                    createdAt: new Date().toISOString(),
                    isActive: true
                },
                {
                    id: 'viewer-001',
                    username: 'viewer',
                    password: 'viewer123',
                    role: Utils.USER_ROLES.VIEWER,
                    name: 'مستخدم عادي',
                    department: 'all',
                    createdAt: new Date().toISOString(),
                    isActive: true
                }
            ];
            
            Utils.StorageManager.set(Utils.STORAGE_KEYS.USERS, defaultUsers);
        }
    }

    /**
     * تسجيل الدخول
     * Login user
     */
    async login(username, password) {
        try {
            Utils.showLoading();
            
            // محاكاة تأخير الشبكة
            await Utils.delay(1000);
            
            const users = Utils.StorageManager.get(Utils.STORAGE_KEYS.USERS, []);
            const user = users.find(u => 
                u.username === username && 
                u.password === password && 
                u.isActive
            );
            
            if (user) {
                // إزالة كلمة المرور من بيانات المستخدم المحفوظة
                const userSession = {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    name: user.name,
                    department: user.department,
                    loginTime: new Date().toISOString()
                };
                
                this.currentUser = userSession;
                Utils.StorageManager.set(Utils.STORAGE_KEYS.CURRENT_USER, userSession);
                
                Utils.hideLoading();
                return { success: true, user: userSession };
            } else {
                Utils.hideLoading();
                return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
            }
        } catch (error) {
            Utils.hideLoading();
            console.error('Login error:', error);
            return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' };
        }
    }

    /**
     * تسجيل الخروج
     * Logout user
     */
    logout() {
        this.currentUser = null;
        Utils.StorageManager.remove(Utils.STORAGE_KEYS.CURRENT_USER);
        
        // إعادة تحميل الصفحة لإعادة تعيين الحالة
        window.location.reload();
    }

    /**
     * التحقق من حالة تسجيل الدخول
     * Check if user is logged in
     */
    isLoggedIn() {
        if (this.currentUser) return true;
        
        const savedUser = Utils.StorageManager.get(Utils.STORAGE_KEYS.CURRENT_USER);
        if (savedUser) {
            this.currentUser = savedUser;
            return true;
        }
        
        return false;
    }

    /**
     * الحصول على المستخدم الحالي
     * Get current user
     */
    getCurrentUser() {
        return this.currentUser || Utils.StorageManager.get(Utils.STORAGE_KEYS.CURRENT_USER);
    }

    /**
     * التحقق من الصلاحية
     * Check permission
     */
    hasPermission(action) {
        const user = this.getCurrentUser();
        if (!user) return false;

        switch (action) {
            case 'view':
                return true; // جميع المستخدمين يمكنهم العرض
            case 'edit':
                return user.role === Utils.USER_ROLES.ADMIN || user.role === Utils.USER_ROLES.MANAGER;
            case 'delete':
                return user.role === Utils.USER_ROLES.ADMIN || user.role === Utils.USER_ROLES.MANAGER;
            case 'manage_users':
                return user.role === Utils.USER_ROLES.ADMIN;
            case 'import_data':
                return user.role === Utils.USER_ROLES.ADMIN;
            case 'export_data':
                return user.role === Utils.USER_ROLES.ADMIN || user.role === Utils.USER_ROLES.MANAGER;
            default:
                return false;
        }
    }

    /**
     * التحقق من صلاحية الوصول للقسم
     * Check department access
     */
    canAccessDepartment(department) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        if (user.role === Utils.USER_ROLES.ADMIN) return true;
        if (user.department === 'all') return true;
        if (user.department === department) return true;
        
        return false;
    }

    /**
     * إضافة مستخدم جديد
     * Add new user
     */
    addUser(userData) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لإضافة مستخدمين');
        }

        const users = Utils.StorageManager.get(Utils.STORAGE_KEYS.USERS, []);
        
        // التحقق من عدم وجود اسم المستخدم مسبقاً
        if (users.find(u => u.username === userData.username)) {
            throw new Error('اسم المستخدم موجود مسبقاً');
        }

        const newUser = {
            id: Utils.generateId(),
            username: userData.username,
            password: userData.password,
            role: userData.role,
            name: userData.name,
            department: userData.department,
            createdAt: new Date().toISOString(),
            isActive: true
        };

        users.push(newUser);
        Utils.StorageManager.set(Utils.STORAGE_KEYS.USERS, users);
        
        return newUser;
    }

    /**
     * تحديث مستخدم
     * Update user
     */
    updateUser(userId, updates) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لتحديث المستخدمين');
        }

        const users = Utils.StorageManager.get(Utils.STORAGE_KEYS.USERS, []);
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex === -1) {
            throw new Error('المستخدم غير موجود');
        }

        users[userIndex] = { ...users[userIndex], ...updates };
        Utils.StorageManager.set(Utils.STORAGE_KEYS.USERS, users);
        
        return users[userIndex];
    }

    /**
     * حذف مستخدم
     * Delete user
     */
    deleteUser(userId) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لحذف المستخدمين');
        }

        const users = Utils.StorageManager.get(Utils.STORAGE_KEYS.USERS, []);
        const filteredUsers = users.filter(u => u.id !== userId);
        
        Utils.StorageManager.set(Utils.STORAGE_KEYS.USERS, filteredUsers);
        return true;
    }

    /**
     * الحصول على جميع المستخدمين
     * Get all users
     */
    getAllUsers() {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لعرض المستخدمين');
        }

        return Utils.StorageManager.get(Utils.STORAGE_KEYS.USERS, [])
            .map(user => ({
                id: user.id,
                username: user.username,
                role: user.role,
                name: user.name,
                department: user.department,
                createdAt: user.createdAt,
                isActive: user.isActive
            }));
    }
}

// إنشاء مثيل عام لمدير المصادقة
window.authManager = new AuthManager();
