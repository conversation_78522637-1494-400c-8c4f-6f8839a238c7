# 🚀 دليل البدء السريع - نظام لوحة تحكم مؤشرات الأداء

## ⚡ التشغيل السريع (أقل من دقيقة)

### الطريقة الأولى: تشغيل مباشر
1. **افتح ملف `index.html` في المتصفح**
2. **سجل دخولك باستخدام:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
3. **استمتع بالنظام! 🎉**

### الطريقة الثانية: خادم محلي
1. **اضغط مرتين على `start.bat` (Windows)**
2. **أو شغل `./start.sh` (Linux/Mac)**
3. **افتح المتصفح على `http://localhost:8000`**
4. **سجل الدخول واستمتع! 🎉**

## 🎯 أول 5 دقائق - جولة سريعة

### 1. تسجيل الدخول (30 ثانية)
- استخدم `admin` / `admin123` للوصول الكامل
- أو `viewer` / `viewer123` للعرض فقط

### 2. استكشاف لوحة المؤشرات (2 دقيقة)
- شاهد 6 مؤشرات تجريبية جاهزة
- جرب الفلاتر (الفترة والقسم)
- اعرض الرسوم البيانية التفاعلية
- راجع الجدول التحليلي

### 3. إضافة مؤشر جديد (1 دقيقة)
- انتقل لتبويب "إدارة البيانات"
- اضغط "إضافة مؤشر جديد"
- املأ البيانات واحفظ
- شاهد المؤشر في اللوحة

### 4. تصدير تقرير (1 دقيقة)
- اضغط "تصدير PDF" لحفظ اللوحة
- أو "تصدير Excel" لحفظ البيانات
- جرب التقارير المختلفة

### 5. استيراد بيانات (30 ثانية)
- استخدم `sample-data.html` لإنشاء ملف Excel
- أو حضر ملفك الخاص
- استورده من "إدارة البيانات"

## 🎨 تخصيص سريع

### تغيير الألوان
```css
/* في css/styles.css */
:root {
    --primary-color: #your-color;
    --success-color: #your-color;
    /* ... */
}
```

### إضافة قسم جديد
```javascript
// في config.js
DEPARTMENTS: {
    'your-dept': 'اسم القسم الجديد'
}
```

### إضافة فترة زمنية
```javascript
// في config.js
PERIODS: {
    '2025-Q1': 'الربع الأول 2025'
}
```

## 🔧 حل المشاكل السريع

### المشكلة: الصفحة لا تحمل
**الحل:** تأكد من تفعيل JavaScript في المتصفح

### المشكلة: الرسوم البيانية لا تظهر
**الحل:** تحقق من اتصال الإنترنت لتحميل Chart.js

### المشكلة: لا يمكن تسجيل الدخول
**الحل:** استخدم البيانات الصحيحة:
- `admin` / `admin123`
- `manager` / `manager123`  
- `viewer` / `viewer123`

### المشكلة: فشل استيراد Excel
**الحل:** تأكد من وجود الأعمدة:
- المؤشر، المستهدف، الفعلي، الفترة، القسم

## 📱 اختبار سريع

### افتح `test.html` واضغط "تشغيل جميع الاختبارات"
- يجب أن تنجح جميع الاختبارات ✅
- إذا فشل اختبار، راجع console للتفاصيل

## 🎉 مبروك!

نظام لوحة تحكم مؤشرات الأداء جاهز للاستخدام!

### الميزات الجاهزة:
✅ 6 مؤشرات تجريبية  
✅ 3 أدوار مستخدمين  
✅ 4 أنواع رسوم بيانية  
✅ تصدير PDF و Excel  
✅ بحث وتصفية متقدمة  
✅ واجهة عربية كاملة  
✅ تشغيل بدون خادم  

### للمساعدة:
- راجع `README.md` للتفاصيل الكاملة
- راجع `DEVELOPER_GUIDE.md` للتطوير المتقدم
- راجع `SYSTEM_OVERVIEW.md` للملخص الشامل

**استمتع باستخدام النظام! 🚀**
