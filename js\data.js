/**
 * نظام إدارة البيانات
 * Data Management System
 */

class DataManager {
    constructor() {
        this.kpis = [];
        this.initializeDefaultKPIs();
        this.loadKPIs();
    }

    /**
     * تهيئة مؤشرات الأداء الافتراضية
     * Initialize default KPIs
     */
    initializeDefaultKPIs() {
        const existingKPIs = Utils.StorageManager.get(Utils.STORAGE_KEYS.KPIS);
        
        if (!existingKPIs || existingKPIs.length === 0) {
            const defaultKPIs = [
                {
                    id: 'kpi-001',
                    name: 'الإنجاز مقابل الخطة',
                    definition: 'نسبة الإنجاز الفعلي مقارنة بالخطة المحددة',
                    target: 100,
                    actual: 95,
                    period: '2024-Q3',
                    department: 'operations',
                    notes: 'أداء جيد مع تحسن ملحوظ',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'kpi-002',
                    name: 'جودة المخرجات',
                    definition: 'مؤشر جودة المنتجات والخدمات المقدمة',
                    target: 95,
                    actual: 88,
                    period: '2024-Q3',
                    department: 'operations',
                    notes: 'يحتاج إلى تحسين في بعض الجوانب',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'kpi-003',
                    name: 'كفاءة استخدام الموارد',
                    definition: 'مدى فعالية استخدام الموارد المتاحة',
                    target: 85,
                    actual: 92,
                    period: '2024-Q3',
                    department: 'finance',
                    notes: 'أداء ممتاز في إدارة الموارد',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'kpi-004',
                    name: 'زمن الإنجاز',
                    definition: 'متوسط الوقت المطلوب لإنجاز المهام',
                    target: 24,
                    actual: 20,
                    period: '2024-Q3',
                    department: 'operations',
                    notes: 'تحسن كبير في سرعة الإنجاز',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'kpi-005',
                    name: 'رضا العملاء',
                    definition: 'مؤشر رضا العملاء عن الخدمات المقدمة',
                    target: 90,
                    actual: 87,
                    period: '2024-Q3',
                    department: 'sales',
                    notes: 'رضا جيد مع إمكانية للتحسين',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'kpi-006',
                    name: 'التحسين المستمر',
                    definition: 'عدد مبادرات التحسين المنفذة',
                    target: 10,
                    actual: 12,
                    period: '2024-Q3',
                    department: 'hr',
                    notes: 'تجاوز المستهدف بنجاح',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ];
            
            // حساب النسب والحالات
            defaultKPIs.forEach(kpi => {
                kpi.percentage = Utils.calculatePercentage(kpi.actual, kpi.target);
                kpi.status = Utils.getKPIStatus(kpi.percentage);
            });
            
            Utils.StorageManager.set(Utils.STORAGE_KEYS.KPIS, defaultKPIs);
        }
    }

    /**
     * تحميل مؤشرات الأداء
     * Load KPIs
     */
    loadKPIs() {
        this.kpis = Utils.StorageManager.get(Utils.STORAGE_KEYS.KPIS, []);
        return this.kpis;
    }

    /**
     * الحصول على جميع مؤشرات الأداء
     * Get all KPIs
     */
    getAllKPIs() {
        return this.kpis;
    }

    /**
     * الحصول على مؤشرات الأداء مع التصفية
     * Get filtered KPIs
     */
    getFilteredKPIs(filters = {}) {
        let filteredKPIs = [...this.kpis];
        
        // تطبيق صلاحيات الوصول للأقسام
        const currentUser = window.authManager.getCurrentUser();
        if (currentUser && currentUser.role === Utils.USER_ROLES.MANAGER) {
            filteredKPIs = filteredKPIs.filter(kpi => 
                window.authManager.canAccessDepartment(kpi.department)
            );
        }
        
        return Utils.filterData(filteredKPIs, filters);
    }

    /**
     * الحصول على مؤشر أداء بالمعرف
     * Get KPI by ID
     */
    getKPIById(id) {
        return this.kpis.find(kpi => kpi.id === id);
    }

    /**
     * إضافة مؤشر أداء جديد
     * Add new KPI
     */
    addKPI(kpiData) {
        // التحقق من الصلاحيات
        if (!window.authManager.hasPermission('edit')) {
            throw new Error('ليس لديك صلاحية لإضافة مؤشرات جديدة');
        }

        // التحقق من صحة البيانات
        const validationErrors = Utils.validateKPI(kpiData);
        if (validationErrors.length > 0) {
            throw new Error(validationErrors.join(', '));
        }

        // التحقق من صلاحية الوصول للقسم
        if (!window.authManager.canAccessDepartment(kpiData.department)) {
            throw new Error('ليس لديك صلاحية للوصول لهذا القسم');
        }

        const newKPI = {
            id: Utils.generateId(),
            name: Utils.sanitizeText(kpiData.name),
            definition: Utils.sanitizeText(kpiData.definition || ''),
            target: parseFloat(kpiData.target),
            actual: parseFloat(kpiData.actual),
            period: kpiData.period,
            department: kpiData.department,
            notes: Utils.sanitizeText(kpiData.notes || ''),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // حساب النسبة والحالة
        newKPI.percentage = Utils.calculatePercentage(newKPI.actual, newKPI.target);
        newKPI.status = Utils.getKPIStatus(newKPI.percentage);

        this.kpis.push(newKPI);
        this.saveKPIs();
        
        return newKPI;
    }

    /**
     * تحديث مؤشر أداء
     * Update KPI
     */
    updateKPI(id, updates) {
        // التحقق من الصلاحيات
        if (!window.authManager.hasPermission('edit')) {
            throw new Error('ليس لديك صلاحية لتحديث المؤشرات');
        }

        const kpiIndex = this.kpis.findIndex(kpi => kpi.id === id);
        if (kpiIndex === -1) {
            throw new Error('المؤشر غير موجود');
        }

        const existingKPI = this.kpis[kpiIndex];
        
        // التحقق من صلاحية الوصول للقسم
        if (!window.authManager.canAccessDepartment(existingKPI.department)) {
            throw new Error('ليس لديك صلاحية لتحديث مؤشرات هذا القسم');
        }

        // تطبيق التحديثات
        const updatedKPI = {
            ...existingKPI,
            ...updates,
            updatedAt: new Date().toISOString()
        };

        // التحقق من صحة البيانات المحدثة
        const validationErrors = Utils.validateKPI(updatedKPI);
        if (validationErrors.length > 0) {
            throw new Error(validationErrors.join(', '));
        }

        // إعادة حساب النسبة والحالة
        updatedKPI.percentage = Utils.calculatePercentage(updatedKPI.actual, updatedKPI.target);
        updatedKPI.status = Utils.getKPIStatus(updatedKPI.percentage);

        this.kpis[kpiIndex] = updatedKPI;
        this.saveKPIs();
        
        return updatedKPI;
    }

    /**
     * حذف مؤشر أداء
     * Delete KPI
     */
    deleteKPI(id) {
        // التحقق من الصلاحيات
        if (!window.authManager.hasPermission('delete')) {
            throw new Error('ليس لديك صلاحية لحذف المؤشرات');
        }

        const kpi = this.getKPIById(id);
        if (!kpi) {
            throw new Error('المؤشر غير موجود');
        }

        // التحقق من صلاحية الوصول للقسم
        if (!window.authManager.canAccessDepartment(kpi.department)) {
            throw new Error('ليس لديك صلاحية لحذف مؤشرات هذا القسم');
        }

        this.kpis = this.kpis.filter(kpi => kpi.id !== id);
        this.saveKPIs();
        
        return true;
    }

    /**
     * حفظ مؤشرات الأداء
     * Save KPIs
     */
    saveKPIs() {
        Utils.StorageManager.set(Utils.STORAGE_KEYS.KPIS, this.kpis);
    }

    /**
     * استيراد بيانات من Excel
     * Import data from Excel
     */
    async importFromExcel(file) {
        if (!window.authManager.hasPermission('import_data')) {
            throw new Error('ليس لديك صلاحية لاستيراد البيانات');
        }

        try {
            const data = await this.readExcelFile(file);
            const importedKPIs = this.processExcelData(data);
            
            // دمج البيانات المستوردة مع البيانات الحالية
            importedKPIs.forEach(kpi => {
                const existingIndex = this.kpis.findIndex(existing => 
                    existing.name === kpi.name && 
                    existing.period === kpi.period && 
                    existing.department === kpi.department
                );
                
                if (existingIndex !== -1) {
                    // تحديث المؤشر الموجود
                    this.kpis[existingIndex] = { ...this.kpis[existingIndex], ...kpi };
                } else {
                    // إضافة مؤشر جديد
                    this.kpis.push(kpi);
                }
            });
            
            this.saveKPIs();
            return { success: true, imported: importedKPIs.length };
        } catch (error) {
            console.error('Excel import error:', error);
            throw new Error('فشل في استيراد ملف Excel: ' + error.message);
        }
    }

    /**
     * قراءة ملف Excel
     * Read Excel file
     */
    readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                    const jsonData = XLSX.utils.sheet_to_json(firstSheet);
                    resolve(jsonData);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * معالجة بيانات Excel
     * Process Excel data
     */
    processExcelData(data) {
        const processedKPIs = [];
        
        data.forEach((row, index) => {
            try {
                // تحويل أسماء الأعمدة المختلفة
                const kpi = {
                    id: Utils.generateId(),
                    name: row['المؤشر'] || row['KPI'] || row['Indicator'] || '',
                    definition: row['التعريف'] || row['Definition'] || row['Description'] || '',
                    target: parseFloat(row['المستهدف'] || row['Target'] || 0),
                    actual: parseFloat(row['الفعلي'] || row['Actual'] || 0),
                    period: row['الفترة'] || row['Period'] || '2024-Q3',
                    department: this.mapDepartment(row['القسم'] || row['Department'] || 'operations'),
                    notes: row['الملاحظات'] || row['Notes'] || row['Comments'] || '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                // التحقق من صحة البيانات الأساسية
                if (kpi.name && kpi.target > 0) {
                    kpi.percentage = Utils.calculatePercentage(kpi.actual, kpi.target);
                    kpi.status = Utils.getKPIStatus(kpi.percentage);
                    processedKPIs.push(kpi);
                }
            } catch (error) {
                console.warn(`خطأ في معالجة الصف ${index + 1}:`, error);
            }
        });
        
        return processedKPIs;
    }

    /**
     * تحويل أسماء الأقسام
     * Map department names
     */
    mapDepartment(departmentName) {
        const departmentMap = {
            'المبيعات': 'sales',
            'التسويق': 'marketing',
            'العمليات': 'operations',
            'الموارد البشرية': 'hr',
            'المالية': 'finance',
            'Sales': 'sales',
            'Marketing': 'marketing',
            'Operations': 'operations',
            'HR': 'hr',
            'Finance': 'finance'
        };
        
        return departmentMap[departmentName] || 'operations';
    }

    /**
     * الحصول على إحصائيات المؤشرات
     * Get KPI statistics
     */
    getKPIStatistics(filters = {}) {
        const filteredKPIs = this.getFilteredKPIs(filters);
        
        const stats = {
            total: filteredKPIs.length,
            excellent: filteredKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.EXCELLENT).length,
            good: filteredKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.GOOD).length,
            poor: filteredKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.POOR).length,
            averagePercentage: 0,
            totalTarget: 0,
            totalActual: 0
        };

        if (filteredKPIs.length > 0) {
            stats.averagePercentage = filteredKPIs.reduce((sum, kpi) => sum + kpi.percentage, 0) / filteredKPIs.length;
            stats.totalTarget = filteredKPIs.reduce((sum, kpi) => sum + kpi.target, 0);
            stats.totalActual = filteredKPIs.reduce((sum, kpi) => sum + kpi.actual, 0);
        }

        return stats;
    }

    /**
     * الحصول على البيانات حسب القسم
     * Get data by department
     */
    getDataByDepartment(filters = {}) {
        const filteredKPIs = this.getFilteredKPIs(filters);
        const departmentData = {};

        filteredKPIs.forEach(kpi => {
            if (!departmentData[kpi.department]) {
                departmentData[kpi.department] = {
                    name: Utils.getDepartmentName(kpi.department),
                    kpis: [],
                    stats: {
                        total: 0,
                        excellent: 0,
                        good: 0,
                        poor: 0,
                        averagePercentage: 0
                    }
                };
            }
            
            departmentData[kpi.department].kpis.push(kpi);
        });

        // حساب الإحصائيات لكل قسم
        Object.keys(departmentData).forEach(dept => {
            const deptKPIs = departmentData[dept].kpis;
            departmentData[dept].stats = {
                total: deptKPIs.length,
                excellent: deptKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.EXCELLENT).length,
                good: deptKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.GOOD).length,
                poor: deptKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.POOR).length,
                averagePercentage: deptKPIs.reduce((sum, kpi) => sum + kpi.percentage, 0) / deptKPIs.length
            };
        });

        return departmentData;
    }

    /**
     * الحصول على البيانات حسب الفترة
     * Get data by period
     */
    getDataByPeriod(filters = {}) {
        const filteredKPIs = this.getFilteredKPIs(filters);
        const periodData = {};

        filteredKPIs.forEach(kpi => {
            if (!periodData[kpi.period]) {
                periodData[kpi.period] = {
                    name: Utils.getPeriodName(kpi.period),
                    kpis: [],
                    stats: {
                        total: 0,
                        excellent: 0,
                        good: 0,
                        poor: 0,
                        averagePercentage: 0
                    }
                };
            }
            
            periodData[kpi.period].kpis.push(kpi);
        });

        // حساب الإحصائيات لكل فترة
        Object.keys(periodData).forEach(period => {
            const periodKPIs = periodData[period].kpis;
            periodData[period].stats = {
                total: periodKPIs.length,
                excellent: periodKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.EXCELLENT).length,
                good: periodKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.GOOD).length,
                poor: periodKPIs.filter(kpi => kpi.status === Utils.KPI_STATUS.POOR).length,
                averagePercentage: periodKPIs.reduce((sum, kpi) => sum + kpi.percentage, 0) / periodKPIs.length
            };
        });

        return periodData;
    }

    /**
     * البحث في المؤشرات
     * Search KPIs
     */
    searchKPIs(searchTerm, filters = {}) {
        const searchFilters = { ...filters, search: searchTerm };
        return this.getFilteredKPIs(searchFilters);
    }

    /**
     * تصدير البيانات
     * Export data
     */
    exportData(format = 'json', filters = {}) {
        const data = this.getFilteredKPIs(filters);
        
        switch (format) {
            case 'excel':
                return Utils.convertToExcelFormat(data);
            case 'json':
                return data;
            default:
                return data;
        }
    }

    /**
     * إعادة تعيين البيانات
     * Reset data
     */
    resetData() {
        if (!window.authManager.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لإعادة تعيين البيانات');
        }

        this.kpis = [];
        Utils.StorageManager.remove(Utils.STORAGE_KEYS.KPIS);
        this.initializeDefaultKPIs();
        this.loadKPIs();
        
        return true;
    }
}

    /**
     * نسخ احتياطي للبيانات
     * Backup data
     */
    backupData() {
        const backup = {
            kpis: this.kpis,
            timestamp: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(backup, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `KPI_Backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        return backup;
    }

    /**
     * استعادة البيانات من النسخ الاحتياطي
     * Restore data from backup
     */
    async restoreData(file) {
        try {
            const text = await file.text();
            const backup = JSON.parse(text);

            if (backup.kpis && Array.isArray(backup.kpis)) {
                this.kpis = backup.kpis;
                this.saveKPIs();
                return { success: true, restored: backup.kpis.length };
            } else {
                throw new Error('تنسيق ملف النسخ الاحتياطي غير صحيح');
            }
        } catch (error) {
            throw new Error('فشل في استعادة البيانات: ' + error.message);
        }
    }

    /**
     * تحليل الأداء
     * Performance analysis
     */
    analyzePerformance(filters = {}) {
        const kpis = this.getFilteredKPIs(filters);

        const analysis = {
            overview: {
                totalKPIs: kpis.length,
                averagePerformance: 0,
                topPerformers: [],
                underPerformers: [],
                trends: {}
            },
            departments: {},
            periods: {},
            recommendations: []
        };

        if (kpis.length === 0) return analysis;

        // حساب متوسط الأداء العام
        analysis.overview.averagePerformance =
            kpis.reduce((sum, kpi) => sum + kpi.percentage, 0) / kpis.length;

        // أفضل وأسوأ المؤشرات
        analysis.overview.topPerformers = kpis
            .filter(kpi => kpi.percentage >= 90)
            .sort((a, b) => b.percentage - a.percentage)
            .slice(0, 5);

        analysis.overview.underPerformers = kpis
            .filter(kpi => kpi.percentage < 70)
            .sort((a, b) => a.percentage - b.percentage)
            .slice(0, 5);

        // تحليل الأقسام
        const deptData = this.getDataByDepartment(filters);
        Object.keys(deptData).forEach(dept => {
            analysis.departments[dept] = {
                name: deptData[dept].name,
                performance: deptData[dept].stats.averagePercentage,
                kpiCount: deptData[dept].stats.total,
                status: this.getDepartmentStatus(deptData[dept].stats.averagePercentage)
            };
        });

        // تحليل الفترات
        const periodData = this.getDataByPeriod(filters);
        Object.keys(periodData).forEach(period => {
            analysis.periods[period] = {
                name: periodData[period].name,
                performance: periodData[period].stats.averagePercentage,
                kpiCount: periodData[period].stats.total,
                trend: this.calculateTrend(period, periodData)
            };
        });

        // توصيات التحسين
        analysis.recommendations = this.generateRecommendations(analysis);

        return analysis;
    }

    /**
     * تحديد حالة القسم
     * Determine department status
     */
    getDepartmentStatus(percentage) {
        if (percentage >= 90) return 'excellent';
        if (percentage >= 70) return 'good';
        return 'poor';
    }

    /**
     * حساب الاتجاه
     * Calculate trend
     */
    calculateTrend(currentPeriod, periodData) {
        // منطق بسيط لحساب الاتجاه
        const periods = Object.keys(periodData).sort();
        const currentIndex = periods.indexOf(currentPeriod);

        if (currentIndex > 0) {
            const previousPeriod = periods[currentIndex - 1];
            const currentPerf = periodData[currentPeriod].stats.averagePerformance;
            const previousPerf = periodData[previousPeriod].stats.averagePerformance;

            if (currentPerf > previousPerf) return 'up';
            if (currentPerf < previousPerf) return 'down';
        }

        return 'stable';
    }

    /**
     * إنتاج توصيات التحسين
     * Generate recommendations
     */
    generateRecommendations(analysis) {
        const recommendations = [];

        // توصيات عامة
        if (analysis.overview.averagePerformance < 70) {
            recommendations.push({
                type: 'critical',
                title: 'تحسين الأداء العام مطلوب',
                description: 'متوسط الأداء العام أقل من 70%، يحتاج إلى خطة تحسين شاملة'
            });
        }

        // توصيات الأقسام
        Object.keys(analysis.departments).forEach(dept => {
            const deptData = analysis.departments[dept];
            if (deptData.status === 'poor') {
                recommendations.push({
                    type: 'department',
                    title: `تحسين أداء ${deptData.name}`,
                    description: `قسم ${deptData.name} يحتاج إلى تركيز خاص لتحسين الأداء`
                });
            }
        });

        // توصيات المؤشرات المتأخرة
        if (analysis.overview.underPerformers.length > 0) {
            recommendations.push({
                type: 'kpis',
                title: 'مراجعة المؤشرات المتأخرة',
                description: `هناك ${analysis.overview.underPerformers.length} مؤشر يحتاج إلى تحسين فوري`
            });
        }

        return recommendations;
    }
}

// إنشاء مثيل عام لمدير البيانات
window.dataManager = new DataManager();
