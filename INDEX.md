# 📋 فهرس المشروع - نظام لوحة تحكم مؤشرات الأداء

## 🎯 نظرة عامة
نظام شامل لإدارة وعرض مؤشرات الأداء الرئيسية (KPIs) باستخدام HTML/CSS/JavaScript مع دعم كامل للعربية و RTL.

## 📁 دليل الملفات

### 🚀 ملفات البدء السريع
| الملف | الوصف | الاستخدام |
|-------|--------|----------|
| `index.html` | **الملف الرئيسي** | افتح هذا الملف لتشغيل النظام |
| `start.bat` | تشغيل Windows | اضغط مرتين للتشغيل على Windows |
| `start.sh` | تشغيل Linux/Mac | `./start.sh` للتشغيل على Linux/Mac |
| `package.json` | إعدادات Node.js | `npm start` للتشغيل بـ Node.js |

### 📖 ملفات التوثيق
| الملف | المحتوى | للمن |
|-------|---------|------|
| `README.md` | **دليل المستخدم الأساسي** | جميع المستخدمين |
| `QUICK_START.md` | **دليل البدء السريع** | المستخدمين الجدد |
| `DEVELOPER_GUIDE.md` | دليل المطور التقني | المطورين |
| `SYSTEM_OVERVIEW.md` | **ملخص شامل للنظام** | المديرين والمطورين |
| `INDEX.md` | فهرس المشروع (هذا الملف) | الجميع |

### 🎨 ملفات التصميم
| المجلد/الملف | المحتوى |
|-------------|---------|
| `css/styles.css` | **جميع الأنماط والتصميم** |
| - متغيرات CSS | الألوان والمقاسات |
| - أنماط RTL | دعم العربية |
| - Responsive Design | التوافق مع الأجهزة |
| - Dark Mode | الوضع المظلم (اختياري) |

### 💻 ملفات JavaScript
| الملف | الوظيفة الرئيسية | المكونات |
|-------|-----------------|----------|
| `config.js` | **إعدادات النظام العامة** | الثوابت والتكوين |
| `js/utils.js` | **الوظائف المساعدة** | أدوات عامة ومرافق |
| `js/auth.js` | **نظام المصادقة** | تسجيل دخول وصلاحيات |
| `js/data.js` | **إدارة البيانات** | CRUD وتخزين |
| `js/charts.js` | **الرسوم البيانية** | Chart.js وتصورات |
| `js/export.js` | **التصدير والطباعة** | PDF وExcel |
| `js/app.js` | **التطبيق الرئيسي** | ربط جميع المكونات |

### 🧪 ملفات الاختبار والأدوات
| الملف | الغرض | كيفية الاستخدام |
|-------|--------|----------------|
| `test.html` | **اختبار شامل للنظام** | افتح في المتصفح واضغط الأزرار |
| `sample-data.html` | إنشاء بيانات Excel تجريبية | لإنشاء ملف Data.xlsx |
| `Data.xlsx` | ملف بيانات تجريبي | للاختبار والاستيراد |

## 🎯 المؤشرات المدعومة

### المؤشرات الافتراضية (6 مؤشرات)
1. **الإنجاز مقابل الخطة** - نسبة تحقيق الأهداف
2. **جودة المخرجات** - مستوى جودة المنتجات/الخدمات
3. **كفاءة استخدام الموارد** - فعالية استغلال الموارد
4. **زمن الإنجاز** - سرعة تنفيذ المهام
5. **رضا العملاء** - مؤشر رضا العملاء (CSI)
6. **التحسين المستمر** - عدد مبادرات التحسين

### إضافة مؤشرات جديدة
- من واجهة "إدارة البيانات"
- أو استيراد من Excel
- أو تعديل `config.js`

## 👥 أدوار المستخدمين

### 🔑 بيانات تسجيل الدخول
| الدور | المستخدم | كلمة المرور | الصلاحيات |
|-------|----------|-------------|-----------|
| **مدير النظام** | `admin` | `admin123` | جميع الصلاحيات |
| **مدير القسم** | `manager` | `manager123` | إدارة قسم المبيعات |
| **قارئ** | `viewer` | `viewer123` | عرض فقط |

### 🛡️ الصلاحيات التفصيلية
| العملية | مدير النظام | مدير القسم | قارئ |
|---------|-------------|-------------|------|
| عرض المؤشرات | ✅ | ✅ | ✅ |
| إضافة مؤشرات | ✅ | ✅* | ❌ |
| تعديل مؤشرات | ✅ | ✅* | ❌ |
| حذف مؤشرات | ✅ | ✅* | ❌ |
| استيراد Excel | ✅ | ❌ | ❌ |
| تصدير التقارير | ✅ | ✅ | ❌ |
| إدارة المستخدمين | ✅ | ❌ | ❌ |

*مقيد بالقسم المخصص

## 📊 أنواع الرسوم البيانية

### الرسوم المتاحة
1. **Bar Chart** - الإنجاز مقابل الخطة
2. **Doughnut Chart** - توزيع التقييمات
3. **Radar Chart** - الأداء حسب القسم
4. **Line Chart** - اتجاهات الأداء
5. **Gauge Charts** - مقاييس فردية

### التلوين الشرطي
- 🟢 **أخضر (≥90%)** - أداء ممتاز
- 🟡 **أصفر (70-89%)** - أداء جيد
- 🔴 **أحمر (<70%)** - يحتاج تحسين

## 📄 تنسيقات التصدير

### التقارير المتاحة
1. **PDF** - لوحة كاملة مع الرسوم
2. **Excel** - بيانات تفصيلية مع إحصائيات
3. **JSON** - نسخة احتياطية كاملة
4. **Print** - طباعة مباشرة

### التقارير المخصصة
- تقرير حسب القسم
- تقرير حسب الفترة
- تقرير الأداء التفصيلي
- نسخة احتياطية شاملة

## 🔍 ميزات البحث والتصفية

### الفلاتر الأساسية
- **الفترة الزمنية** - اختيار ربع سنوي
- **القسم** - تصفية حسب القسم
- **البحث النصي** - في اسم المؤشر والملاحظات

### الفلاتر المتقدمة
- نطاق النسب المئوية
- التقييم (ممتاز/جيد/يحتاج تحسين)
- نطاق التواريخ
- حالة المؤشر

## 🛠️ التقنيات المستخدمة

### المكتبات الخارجية
- **Chart.js** - الرسوم البيانية
- **SheetJS** - قراءة/كتابة Excel
- **jsPDF** - تصدير PDF
- **html2canvas** - التقاط الشاشة
- **Font Awesome** - الأيقونات
- **Google Fonts** - خط Cairo العربي

### التقنيات الأساسية
- **HTML5** - البنية الأساسية
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6+** - المنطق والتفاعل
- **LocalStorage** - التخزين المحلي
- **Responsive Design** - التوافق مع الأجهزة

## 📈 إحصائيات المشروع

### حجم المشروع
- **17 ملف** إجمالي
- **~2000 سطر** من الكود
- **7 مكونات** رئيسية
- **6 مؤشرات** افتراضية
- **3 أدوار** مستخدمين

### الميزات المطبقة
- ✅ **100%** من المتطلبات الأساسية
- ✅ **ميزات إضافية** متقدمة
- ✅ **توثيق شامل** ومفصل
- ✅ **اختبارات** شاملة
- ✅ **دعم متعدد المنصات**

## 🎊 تهانينا!

لقد تم إنشاء نظام لوحة تحكم مؤشرات الأداء بنجاح!

### 🚀 للبدء الفوري:
1. افتح `index.html`
2. سجل دخولك بـ `admin` / `admin123`
3. استمتع بالنظام!

### 📚 للتعلم أكثر:
- ابدأ بـ `QUICK_START.md`
- ثم راجع `README.md`
- للتطوير: `DEVELOPER_GUIDE.md`

**النظام جاهز للاستخدام الفوري! 🎉**
