/**
 * التطبيق الرئيسي
 * Main Application
 */

class KPIDashboardApp {
    constructor() {
        this.currentTab = 'dashboard';
        this.currentFilters = {
            period: 'all',
            department: 'all',
            search: ''
        };
        this.editingKPI = null;
        
        this.init();
    }

    /**
     * تهيئة التطبيق
     * Initialize application
     */
    async init() {
        try {
            // التحقق من حالة تسجيل الدخول
            if (window.authManager.isLoggedIn()) {
                this.showMainApp();
            } else {
                this.showLoginModal();
            }
            
            // ربط الأحداث
            this.bindEvents();
            
        } catch (error) {
            console.error('App initialization error:', error);
            Utils.showToast('حدث خطأ في تهيئة التطبيق', 'error');
        }
    }

    /**
     * عرض نافذة تسجيل الدخول
     * Show login modal
     */
    showLoginModal() {
        document.getElementById('loginModal').classList.remove('hidden');
        document.getElementById('app').classList.add('hidden');
    }

    /**
     * عرض التطبيق الرئيسي
     * Show main application
     */
    showMainApp() {
        document.getElementById('loginModal').classList.add('hidden');
        document.getElementById('app').classList.remove('hidden');
        
        // تطبيق الصلاحيات
        const user = window.authManager.getCurrentUser();
        Utils.applyPermissions(user.role);
        
        // تحديث معلومات المستخدم
        document.getElementById('userWelcome').textContent = `مرحباً، ${user.name}`;
        
        // تحميل البيانات وعرض اللوحة
        this.loadDashboard();
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // تسجيل الخروج
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // التنقل بين التبويبات
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // الفلاتر
        document.getElementById('periodFilter').addEventListener('change', (e) => {
            this.currentFilters.period = e.target.value;
            this.applyFilters();
        });

        document.getElementById('departmentFilter').addEventListener('change', (e) => {
            this.currentFilters.department = e.target.value;
            this.applyFilters();
        });

        document.getElementById('refreshDashboard').addEventListener('click', () => {
            this.loadDashboard();
        });

        // البحث في الجدول
        document.getElementById('searchTable').addEventListener('input', 
            Utils.debounce((e) => {
                this.currentFilters.search = e.target.value;
                this.updateTable();
            }, 300)
        );

        // إدارة البيانات
        document.getElementById('addKpiBtn')?.addEventListener('click', () => {
            this.showKPIModal();
        });

        document.getElementById('importExcelBtn')?.addEventListener('click', () => {
            document.getElementById('excelFileInput').click();
        });

        document.getElementById('excelFileInput').addEventListener('change', (e) => {
            this.handleExcelImport(e.target.files[0]);
        });

        // نافذة المؤشر
        document.getElementById('closeModal').addEventListener('click', () => {
            this.hideKPIModal();
        });

        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.hideKPIModal();
        });

        document.getElementById('kpiForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleKPISubmit();
        });

        // التصدير
        document.getElementById('exportExcel').addEventListener('click', () => {
            window.exportManager.exportTableToExcel(this.currentFilters);
        });

        document.getElementById('exportPDF').addEventListener('click', () => {
            window.exportManager.exportDashboardToPDF();
        });

        // إغلاق النوافذ عند النقر خارجها
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.classList.add('hidden');
            }
        });
    }

    /**
     * معالجة تسجيل الدخول
     * Handle login
     */
    async handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        const result = await window.authManager.login(username, password);
        
        if (result.success) {
            Utils.showToast(`مرحباً ${result.user.name}`, 'success');
            this.showMainApp();
        } else {
            Utils.showToast(result.message, 'error');
        }
    }

    /**
     * معالجة تسجيل الخروج
     * Handle logout
     */
    handleLogout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            window.authManager.logout();
        }
    }

    /**
     * التبديل بين التبويبات
     * Switch tabs
     */
    switchTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });

        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(tabName).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        this.currentTab = tabName;

        // تحميل محتوى التبويب
        switch (tabName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'data-management':
                this.loadDataManagement();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'users':
                this.loadUsers();
                break;
        }
    }

    /**
     * تحميل لوحة المؤشرات
     * Load dashboard
     */
    loadDashboard() {
        this.updateKPICards();
        this.updateTable();
        
        // تأخير تحميل الرسوم البيانية لضمان عرض العناصر
        setTimeout(() => {
            window.chartsManager.initializeCharts();
        }, 100);
    }

    /**
     * تحديث بطاقات المؤشرات
     * Update KPI cards
     */
    updateKPICards() {
        const kpis = window.dataManager.getFilteredKPIs(this.currentFilters);
        const kpiGrid = document.getElementById('kpiGrid');
        
        kpiGrid.innerHTML = '';

        kpis.forEach(kpi => {
            const card = this.createKPICard(kpi);
            kpiGrid.appendChild(card);
        });
    }

    /**
     * إنشاء بطاقة مؤشر
     * Create KPI card
     */
    createKPICard(kpi) {
        const card = document.createElement('div');
        card.className = 'kpi-card';
        card.innerHTML = `
            <div class="kpi-header">
                <div class="kpi-title">${kpi.name}</div>
                <div class="kpi-status">${Utils.getStatusIcon(kpi.status)}</div>
            </div>
            <div class="kpi-values">
                <div class="kpi-value">
                    <div class="kpi-value-label">المستهدف</div>
                    <div class="kpi-value-number">${Utils.formatNumber(kpi.target)}</div>
                </div>
                <div class="kpi-value">
                    <div class="kpi-value-label">الفعلي</div>
                    <div class="kpi-value-number">${Utils.formatNumber(kpi.actual)}</div>
                </div>
            </div>
            <div class="kpi-percentage">
                <div class="percentage-value status-${kpi.status}">
                    ${Utils.formatNumber(kpi.percentage)}%
                </div>
                <div class="percentage-label">نسبة الإنجاز</div>
            </div>
            <div class="kpi-info">
                <small><strong>القسم:</strong> ${Utils.getDepartmentName(kpi.department)}</small><br>
                <small><strong>الفترة:</strong> ${Utils.getPeriodName(kpi.period)}</small>
                ${kpi.notes ? `<br><small><strong>ملاحظات:</strong> ${kpi.notes}</small>` : ''}
            </div>
        `;
        
        return card;
    }

    /**
     * تحديث الجدول
     * Update table
     */
    updateTable() {
        const kpis = window.dataManager.getFilteredKPIs(this.currentFilters);
        const tableBody = document.getElementById('kpiTableBody');
        
        tableBody.innerHTML = '';

        kpis.forEach(kpi => {
            const row = this.createTableRow(kpi);
            tableBody.appendChild(row);
        });
    }

    /**
     * إنشاء صف في الجدول
     * Create table row
     */
    createTableRow(kpi) {
        const row = document.createElement('tr');
        const canEdit = window.authManager.hasPermission('edit') &&
                       window.authManager.canAccessDepartment(kpi.department);

        // إضافة شريط التقدم للنسبة
        const progressBar = `
            <div class="progress-bar">
                <div class="progress-fill ${kpi.status}" style="width: ${Math.min(kpi.percentage, 100)}%"></div>
            </div>
        `;

        row.innerHTML = `
            <td>
                <div class="kpi-name-cell">
                    <strong>${kpi.name}</strong>
                    ${kpi.definition ? `<br><small class="text-muted">${kpi.definition}</small>` : ''}
                </div>
            </td>
            <td class="text-center">${Utils.formatNumber(kpi.target)}</td>
            <td class="text-center">${Utils.formatNumber(kpi.actual)}</td>
            <td class="text-center">
                <div class="percentage-cell">
                    <span class="status-${kpi.status}">${Utils.formatNumber(kpi.percentage)}%</span>
                    ${progressBar}
                </div>
            </td>
            <td class="text-center">
                <span class="status-indicator ${kpi.status}">
                    ${Utils.getStatusIcon(kpi.status)}
                    ${kpi.status === 'excellent' ? 'ممتاز' :
                      kpi.status === 'good' ? 'جيد' : 'يحتاج تحسين'}
                </span>
            </td>
            <td>
                <div class="notes-cell">
                    ${kpi.notes || '-'}
                </div>
            </td>
            <td class="text-center">${Utils.getPeriodName(kpi.period)}</td>
            <td class="text-center">${Utils.getDepartmentName(kpi.department)}</td>
            <td class="admin-manager-only text-center">
                ${canEdit ? `
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-warning" onclick="app.editKPI('${kpi.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="app.deleteKPI('${kpi.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="app.viewKPIDetails('${kpi.id}')" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                ` : '-'}
            </td>
        `;

        return row;
    }

    /**
     * تطبيق الفلاتر
     * Apply filters
     */
    applyFilters() {
        this.updateKPICards();
        this.updateTable();
        window.chartsManager.updateCharts();
    }

    /**
     * عرض نافذة المؤشر
     * Show KPI modal
     */
    showKPIModal(kpi = null) {
        this.editingKPI = kpi;
        const modal = document.getElementById('kpiModal');
        const form = document.getElementById('kpiForm');
        const title = document.getElementById('modalTitle');
        
        if (kpi) {
            title.textContent = 'تعديل المؤشر';
            this.populateKPIForm(kpi);
        } else {
            title.textContent = 'إضافة مؤشر جديد';
            form.reset();
        }
        
        modal.classList.remove('hidden');
    }

    /**
     * إخفاء نافذة المؤشر
     * Hide KPI modal
     */
    hideKPIModal() {
        document.getElementById('kpiModal').classList.add('hidden');
        document.getElementById('kpiForm').reset();
        this.editingKPI = null;
    }

    /**
     * ملء نموذج المؤشر
     * Populate KPI form
     */
    populateKPIForm(kpi) {
        document.getElementById('kpiName').value = kpi.name;
        document.getElementById('kpiDefinition').value = kpi.definition;
        document.getElementById('kpiTarget').value = kpi.target;
        document.getElementById('kpiActual').value = kpi.actual;
        document.getElementById('kpiPeriod').value = kpi.period;
        document.getElementById('kpiDepartment').value = kpi.department;
        document.getElementById('kpiNotes').value = kpi.notes;
    }

    /**
     * معالجة إرسال نموذج المؤشر
     * Handle KPI form submission
     */
    async handleKPISubmit() {
        try {
            const formData = {
                name: document.getElementById('kpiName').value,
                definition: document.getElementById('kpiDefinition').value,
                target: parseFloat(document.getElementById('kpiTarget').value),
                actual: parseFloat(document.getElementById('kpiActual').value),
                period: document.getElementById('kpiPeriod').value,
                department: document.getElementById('kpiDepartment').value,
                notes: document.getElementById('kpiNotes').value
            };

            if (this.editingKPI) {
                // تحديث مؤشر موجود
                await window.dataManager.updateKPI(this.editingKPI.id, formData);
                Utils.showToast('تم تحديث المؤشر بنجاح', 'success');
            } else {
                // إضافة مؤشر جديد
                await window.dataManager.addKPI(formData);
                Utils.showToast('تم إضافة المؤشر بنجاح', 'success');
            }

            this.hideKPIModal();
            this.loadDashboard();

        } catch (error) {
            console.error('KPI submit error:', error);
            Utils.showToast(error.message, 'error');
        }
    }

    /**
     * تعديل مؤشر
     * Edit KPI
     */
    editKPI(kpiId) {
        const kpi = window.dataManager.getKPIById(kpiId);
        if (kpi) {
            this.showKPIModal(kpi);
        }
    }

    /**
     * حذف مؤشر
     * Delete KPI
     */
    async deleteKPI(kpiId) {
        if (confirm('هل أنت متأكد من حذف هذا المؤشر؟')) {
            try {
                await window.dataManager.deleteKPI(kpiId);
                Utils.showToast('تم حذف المؤشر بنجاح', 'success');
                this.loadDashboard();
            } catch (error) {
                console.error('Delete KPI error:', error);
                Utils.showToast(error.message, 'error');
            }
        }
    }

    /**
     * معالجة استيراد Excel
     * Handle Excel import
     */
    async handleExcelImport(file) {
        if (!file) return;

        try {
            const result = await window.dataManager.importFromExcel(file);
            Utils.showToast(`تم استيراد ${result.imported} مؤشر بنجاح`, 'success');
            this.loadDashboard();
        } catch (error) {
            console.error('Excel import error:', error);
            Utils.showToast(error.message, 'error');
        }

        // إعادة تعيين input الملف
        document.getElementById('excelFileInput').value = '';
    }

    /**
     * تحميل إدارة البيانات
     * Load data management
     */
    loadDataManagement() {
        // تحديث الجدول في تبويب إدارة البيانات
        this.updateTable();
    }

    /**
     * تحميل التقارير
     * Load reports
     */
    loadReports() {
        // يمكن إضافة منطق خاص بالتقارير هنا
        console.log('Loading reports...');
    }

    /**
     * تحميل إدارة المستخدمين
     * Load users management
     */
    loadUsers() {
        try {
            const users = window.authManager.getAllUsers();
            this.updateUsersTable(users);
        } catch (error) {
            console.error('Load users error:', error);
            Utils.showToast(error.message, 'error');
        }
    }

    /**
     * تحديث جدول المستخدمين
     * Update users table
     */
    updateUsersTable(users) {
        const tableBody = document.getElementById('usersTableBody');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.username}</td>
                <td>${this.getRoleName(user.role)}</td>
                <td>${Utils.getDepartmentName(user.department)}</td>
                <td>${Utils.formatDate(user.createdAt)}</td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="app.editUser('${user.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteUser('${user.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    /**
     * الحصول على اسم الدور
     * Get role name
     */
    getRoleName(role) {
        const roleNames = {
            [Utils.USER_ROLES.ADMIN]: 'مدير النظام',
            [Utils.USER_ROLES.MANAGER]: 'مدير القسم',
            [Utils.USER_ROLES.VIEWER]: 'قارئ'
        };
        return roleNames[role] || role;
    }

    /**
     * تعديل مستخدم
     * Edit user
     */
    editUser(userId) {
        // يمكن إضافة نافذة تعديل المستخدم هنا
        Utils.showToast('ميزة تعديل المستخدم قيد التطوير', 'info');
    }

    /**
     * حذف مستخدم
     * Delete user
     */
    async deleteUser(userId) {
        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            try {
                await window.authManager.deleteUser(userId);
                Utils.showToast('تم حذف المستخدم بنجاح', 'success');
                this.loadUsers();
            } catch (error) {
                console.error('Delete user error:', error);
                Utils.showToast(error.message, 'error');
            }
        }
    }

    /**
     * تحديث الإحصائيات
     * Update statistics
     */
    updateStatistics() {
        const stats = window.dataManager.getKPIStatistics(this.currentFilters);
        
        // يمكن إضافة عرض الإحصائيات في مكان مخصص
        console.log('Statistics updated:', stats);
    }

    /**
     * تحديث الفلاتر المتاحة
     * Update available filters
     */
    updateAvailableFilters() {
        const kpis = window.dataManager.getAllKPIs();
        
        // تحديث فلتر الأقسام
        const departments = [...new Set(kpis.map(kpi => kpi.department))];
        const departmentFilter = document.getElementById('departmentFilter');
        
        // الاحتفاظ بالخيار الحالي
        const currentDept = departmentFilter.value;
        
        departmentFilter.innerHTML = '<option value="all">جميع الأقسام</option>';
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept;
            option.textContent = Utils.getDepartmentName(dept);
            departmentFilter.appendChild(option);
        });
        
        departmentFilter.value = currentDept;

        // تحديث فلتر الفترات
        const periods = [...new Set(kpis.map(kpi => kpi.period))];
        const periodFilter = document.getElementById('periodFilter');
        
        const currentPeriod = periodFilter.value;
        
        periodFilter.innerHTML = '<option value="all">جميع الفترات</option>';
        periods.forEach(period => {
            const option = document.createElement('option');
            option.value = period;
            option.textContent = Utils.getPeriodName(period);
            periodFilter.appendChild(option);
        });
        
        periodFilter.value = currentPeriod;
    }

    /**
     * عرض تفاصيل المؤشر
     * View KPI details
     */
    viewKPIDetails(kpiId) {
        const kpi = window.dataManager.getKPIById(kpiId);
        if (!kpi) return;

        const detailsHtml = `
            <div class="kpi-details">
                <h3>${kpi.name}</h3>
                <div class="details-grid">
                    <div class="detail-item">
                        <label>التعريف:</label>
                        <p>${kpi.definition || 'غير محدد'}</p>
                    </div>
                    <div class="detail-item">
                        <label>المستهدف:</label>
                        <p>${Utils.formatNumber(kpi.target)}</p>
                    </div>
                    <div class="detail-item">
                        <label>الفعلي:</label>
                        <p>${Utils.formatNumber(kpi.actual)}</p>
                    </div>
                    <div class="detail-item">
                        <label>النسبة:</label>
                        <p class="status-${kpi.status}">${Utils.formatNumber(kpi.percentage)}%</p>
                    </div>
                    <div class="detail-item">
                        <label>التقييم:</label>
                        <p>${Utils.getStatusIcon(kpi.status)} ${this.getStatusText(kpi.status)}</p>
                    </div>
                    <div class="detail-item">
                        <label>القسم:</label>
                        <p>${Utils.getDepartmentName(kpi.department)}</p>
                    </div>
                    <div class="detail-item">
                        <label>الفترة:</label>
                        <p>${Utils.getPeriodName(kpi.period)}</p>
                    </div>
                    <div class="detail-item full-width">
                        <label>الملاحظات:</label>
                        <p>${kpi.notes || 'لا توجد ملاحظات'}</p>
                    </div>
                    <div class="detail-item">
                        <label>تاريخ الإنشاء:</label>
                        <p>${Utils.formatDate(kpi.createdAt)}</p>
                    </div>
                    <div class="detail-item">
                        <label>آخر تحديث:</label>
                        <p>${Utils.formatDate(kpi.updatedAt)}</p>
                    </div>
                </div>
            </div>
        `;

        this.showCustomModal('تفاصيل المؤشر', detailsHtml);
    }

    /**
     * عرض نافذة مخصصة
     * Show custom modal
     */
    showCustomModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button type="button" class="close-btn" onclick="this.closest('.modal').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إغلاق عند النقر خارج النافذة
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * الحصول على نص الحالة
     * Get status text
     */
    getStatusText(status) {
        const statusTexts = {
            'excellent': 'ممتاز',
            'good': 'جيد',
            'poor': 'يحتاج تحسين'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * ترتيب الجدول
     * Sort table
     */
    sortTable(column, direction = 'asc') {
        const kpis = window.dataManager.getFilteredKPIs(this.currentFilters);

        kpis.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            // تحويل النصوص للمقارنة
            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            if (direction === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });

        this.renderSortedTable(kpis);
    }

    /**
     * عرض الجدول المرتب
     * Render sorted table
     */
    renderSortedTable(kpis) {
        const tableBody = document.getElementById('kpiTableBody');
        tableBody.innerHTML = '';

        kpis.forEach(kpi => {
            const row = this.createTableRow(kpi);
            tableBody.appendChild(row);
        });
    }

    /**
     * تصفية متقدمة
     * Advanced filtering
     */
    showAdvancedFilters() {
        const filtersHtml = `
            <div class="advanced-filters">
                <h4>تصفية متقدمة</h4>
                <div class="filter-grid">
                    <div class="filter-group">
                        <label>النسبة من:</label>
                        <input type="number" id="percentageFrom" min="0" max="100">
                    </div>
                    <div class="filter-group">
                        <label>النسبة إلى:</label>
                        <input type="number" id="percentageTo" min="0" max="100">
                    </div>
                    <div class="filter-group">
                        <label>التقييم:</label>
                        <select id="statusFilter">
                            <option value="">جميع التقييمات</option>
                            <option value="excellent">ممتاز</option>
                            <option value="good">جيد</option>
                            <option value="poor">يحتاج تحسين</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>تاريخ الإنشاء من:</label>
                        <input type="date" id="dateFrom">
                    </div>
                    <div class="filter-group">
                        <label>تاريخ الإنشاء إلى:</label>
                        <input type="date" id="dateTo">
                    </div>
                </div>
                <div class="filter-actions">
                    <button type="button" class="btn btn-primary" onclick="app.applyAdvancedFilters()">
                        <i class="fas fa-filter"></i> تطبيق الفلاتر
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="app.clearAdvancedFilters()">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                </div>
            </div>
        `;

        this.showCustomModal('تصفية متقدمة', filtersHtml);
    }

    /**
     * تطبيق الفلاتر المتقدمة
     * Apply advanced filters
     */
    applyAdvancedFilters() {
        const percentageFrom = document.getElementById('percentageFrom').value;
        const percentageTo = document.getElementById('percentageTo').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;

        let kpis = window.dataManager.getFilteredKPIs(this.currentFilters);

        // تطبيق فلاتر النسبة
        if (percentageFrom) {
            kpis = kpis.filter(kpi => kpi.percentage >= parseFloat(percentageFrom));
        }
        if (percentageTo) {
            kpis = kpis.filter(kpi => kpi.percentage <= parseFloat(percentageTo));
        }

        // تطبيق فلتر التقييم
        if (statusFilter) {
            kpis = kpis.filter(kpi => kpi.status === statusFilter);
        }

        // تطبيق فلاتر التاريخ
        if (dateFrom) {
            kpis = kpis.filter(kpi => new Date(kpi.createdAt) >= new Date(dateFrom));
        }
        if (dateTo) {
            kpis = kpis.filter(kpi => new Date(kpi.createdAt) <= new Date(dateTo));
        }

        this.renderSortedTable(kpis);

        // إغلاق النافذة
        document.querySelector('.modal').remove();

        Utils.showToast(`تم العثور على ${kpis.length} مؤشر`, 'info');
    }

    /**
     * مسح الفلاتر المتقدمة
     * Clear advanced filters
     */
    clearAdvancedFilters() {
        this.updateTable();
        document.querySelector('.modal').remove();
        Utils.showToast('تم مسح الفلاتر', 'info');
    }

    /**
     * إعادة تحميل البيانات
     * Reload data
     */
    reloadData() {
        window.dataManager.loadKPIs();
        this.updateAvailableFilters();
        this.loadDashboard();
    }

    /**
     * عرض إحصائيات سريعة
     * Show quick statistics
     */
    showQuickStats() {
        const stats = window.dataManager.getKPIStatistics(this.currentFilters);
        const analysis = window.dataManager.analyzePerformance(this.currentFilters);

        const statsHtml = `
            <div class="quick-stats">
                <div class="stats-grid">
                    <div class="stat-card excellent">
                        <div class="stat-number">${stats.excellent}</div>
                        <div class="stat-label">أداء ممتاز</div>
                    </div>
                    <div class="stat-card good">
                        <div class="stat-number">${stats.good}</div>
                        <div class="stat-label">أداء جيد</div>
                    </div>
                    <div class="stat-card poor">
                        <div class="stat-number">${stats.poor}</div>
                        <div class="stat-label">يحتاج تحسين</div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-number">${stats.total}</div>
                        <div class="stat-label">إجمالي المؤشرات</div>
                    </div>
                </div>
                <div class="performance-summary">
                    <h4>ملخص الأداء</h4>
                    <p><strong>متوسط الأداء العام:</strong> ${Utils.formatNumber(stats.averagePercentage)}%</p>
                    <p><strong>إجمالي المستهدف:</strong> ${Utils.formatNumber(stats.totalTarget)}</p>
                    <p><strong>إجمالي الفعلي:</strong> ${Utils.formatNumber(stats.totalActual)}</p>
                </div>
                ${analysis.recommendations.length > 0 ? `
                    <div class="recommendations">
                        <h4>توصيات التحسين</h4>
                        ${analysis.recommendations.map(rec => `
                            <div class="recommendation ${rec.type}">
                                <strong>${rec.title}</strong>
                                <p>${rec.description}</p>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        this.showCustomModal('إحصائيات سريعة', statsHtml);
    }

    /**
     * إضافة أحداث الترتيب للجدول
     * Add sorting events to table
     */
    addTableSortingEvents() {
        const headers = document.querySelectorAll('#kpiTable th');
        headers.forEach((header, index) => {
            if (index < 8) { // تجاهل عمود الإجراءات
                header.classList.add('sortable-header');
                header.addEventListener('click', () => {
                    this.handleTableSort(index, header);
                });
            }
        });
    }

    /**
     * معالجة ترتيب الجدول
     * Handle table sorting
     */
    handleTableSort(columnIndex, headerElement) {
        const columns = ['name', 'target', 'actual', 'percentage', 'status', 'notes', 'period', 'department'];
        const column = columns[columnIndex];

        // تحديد اتجاه الترتيب
        let direction = 'asc';
        if (headerElement.classList.contains('asc')) {
            direction = 'desc';
        }

        // إزالة فئات الترتيب من جميع الرؤوس
        document.querySelectorAll('.sortable-header').forEach(h => {
            h.classList.remove('asc', 'desc');
        });

        // إضافة فئة الترتيب للرأس الحالي
        headerElement.classList.add(direction);

        // تطبيق الترتيب
        this.sortTable(column, direction);
    }

    /**
     * إضافة شريط أدوات للجدول
     * Add table toolbar
     */
    addTableToolbar() {
        const tableHeader = document.querySelector('.table-header');
        const toolbar = document.createElement('div');
        toolbar.className = 'table-toolbar';
        toolbar.innerHTML = `
            <div class="toolbar-left">
                <button type="button" class="btn btn-sm btn-info" onclick="app.showQuickStats()">
                    <i class="fas fa-chart-bar"></i> إحصائيات
                </button>
                <button type="button" class="btn btn-sm btn-secondary" onclick="app.showAdvancedFilters()">
                    <i class="fas fa-filter"></i> فلاتر متقدمة
                </button>
                <button type="button" class="btn btn-sm btn-primary" onclick="app.refreshTable()">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
            </div>
            <div class="toolbar-right">
                <span class="record-count">عدد السجلات: <span id="recordCount">0</span></span>
            </div>
        `;

        tableHeader.appendChild(toolbar);
    }

    /**
     * تحديث عداد السجلات
     * Update record count
     */
    updateRecordCount() {
        const recordCountElement = document.getElementById('recordCount');
        if (recordCountElement) {
            const visibleRows = document.querySelectorAll('#kpiTableBody tr').length;
            recordCountElement.textContent = visibleRows;
        }
    }

    /**
     * تحديث الجدول
     * Refresh table
     */
    refreshTable() {
        this.updateTable();
        this.updateRecordCount();
        Utils.showToast('تم تحديث الجدول', 'success');
    }

    /**
     * تصدير البيانات المرئية
     * Export visible data
     */
    exportVisibleData(format = 'excel') {
        const visibleKPIs = window.dataManager.getFilteredKPIs(this.currentFilters);

        switch (format) {
            case 'excel':
                window.exportManager.exportTableToExcel(this.currentFilters);
                break;
            case 'json':
                window.exportManager.exportToJSON(this.currentFilters);
                break;
            case 'pdf':
                window.exportManager.exportDashboardToPDF();
                break;
        }
    }

    /**
     * إعداد اختصارات لوحة المفاتيح
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S للحفظ السريع
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (this.editingKPI) {
                    this.handleKPISubmit();
                }
            }

            // Escape لإغلاق النوافذ
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal:not(.hidden)');
                modals.forEach(modal => {
                    modal.classList.add('hidden');
                });
                this.hideKPIModal();
            }

            // Ctrl/Cmd + N لإضافة مؤشر جديد
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                if (window.authManager.hasPermission('edit')) {
                    this.showKPIModal();
                }
            }

            // F5 لتحديث البيانات
            if (e.key === 'F5') {
                e.preventDefault();
                this.reloadData();
            }
        });
    }

    /**
     * إعداد التحديث التلقائي
     * Setup auto refresh
     */
    setupAutoRefresh(intervalMinutes = 5) {
        setInterval(() => {
            if (this.currentTab === 'dashboard') {
                this.loadDashboard();
                console.log('Auto refresh executed');
            }
        }, intervalMinutes * 60 * 1000);
    }

    /**
     * حفظ إعدادات المستخدم
     * Save user preferences
     */
    saveUserPreferences() {
        const preferences = {
            currentTab: this.currentTab,
            filters: this.currentFilters,
            lastLogin: new Date().toISOString()
        };

        Utils.StorageManager.set('user_preferences', preferences);
    }

    /**
     * تحميل إعدادات المستخدم
     * Load user preferences
     */
    loadUserPreferences() {
        const preferences = Utils.StorageManager.get('user_preferences');
        if (preferences) {
            this.currentFilters = preferences.filters || this.currentFilters;

            // تطبيق الفلاتر المحفوظة
            document.getElementById('periodFilter').value = this.currentFilters.period;
            document.getElementById('departmentFilter').value = this.currentFilters.department;
            document.getElementById('searchTable').value = this.currentFilters.search;
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new KPIDashboardApp();

    // إعداد اختصارات لوحة المفاتيح
    window.app.setupKeyboardShortcuts();

    // إعداد التحديث التلقائي (كل 5 دقائق)
    window.app.setupAutoRefresh(5);

    // إضافة شريط أدوات الجدول
    setTimeout(() => {
        window.app.addTableToolbar();
        window.app.addTableSortingEvents();
    }, 1000);
});

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new KPIDashboardApp();
});

// إضافة أنماط CSS للأزرار الصغيرة
const style = document.createElement('style');
style.textContent = `
    .btn-sm {
        padding: 5px 10px;
        font-size: 12px;
        margin: 0 2px;
    }
    
    .kpi-info {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
        color: var(--text-secondary);
        font-size: 12px;
        line-height: 1.4;
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
