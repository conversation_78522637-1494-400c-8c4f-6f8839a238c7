# دليل المطور - نظام لوحة تحكم مؤشرات الأداء

## البنية التقنية

### هيكل الملفات
```
KPI Dashboard/
├── index.html              # الواجهة الرئيسية
├── config.js               # ملف التكوين العام
├── css/
│   └── styles.css         # الأنماط والتصميم
├── js/
│   ├── utils.js           # الوظائف المساعدة والثوابت
│   ├── auth.js            # نظام المصادقة والصلاحيات
│   ├── data.js            # إدارة البيانات والـ CRUD
│   ├── charts.js          # الرسوم البيانية
│   ├── export.js          # التصدير والطباعة
│   └── app.js             # التطبيق الرئيسي والتحكم
├── sample-data.html       # أداة إنشاء بيانات تجريبية
├── Data.xlsx              # ملف بيانات تجريبي
└── README.md              # دليل المستخدم
```

## المكونات الرئيسية

### 1. نظام المصادقة (auth.js)
```javascript
class AuthManager {
    // إدارة تسجيل الدخول والخروج
    // التحقق من الصلاحيات
    // إدارة المستخدمين
}
```

**الوظائف الرئيسية:**
- `login(username, password)` - تسجيل الدخول
- `logout()` - تسجيل الخروج
- `hasPermission(action)` - التحقق من الصلاحية
- `canAccessDepartment(department)` - التحقق من صلاحية القسم

### 2. إدارة البيانات (data.js)
```javascript
class DataManager {
    // عمليات CRUD للمؤشرات
    // استيراد وتصدير البيانات
    // التحليل والإحصائيات
}
```

**الوظائف الرئيسية:**
- `addKPI(kpiData)` - إضافة مؤشر جديد
- `updateKPI(id, updates)` - تحديث مؤشر
- `deleteKPI(id)` - حذف مؤشر
- `importFromExcel(file)` - استيراد من Excel
- `getKPIStatistics(filters)` - الحصول على الإحصائيات

### 3. الرسوم البيانية (charts.js)
```javascript
class ChartsManager {
    // إنشاء وإدارة الرسوم البيانية
    // تحديث البيانات
    // تصدير الرسوم
}
```

**أنواع الرسوم المدعومة:**
- Bar Chart - الإنجاز مقابل الخطة
- Doughnut Chart - توزيع التقييمات
- Radar Chart - الأداء حسب القسم
- Line Chart - اتجاهات الأداء
- Gauge Chart - مقاييس فردية

### 4. التصدير (export.js)
```javascript
class ExportManager {
    // تصدير PDF و Excel
    // تقارير مخصصة
    // طباعة
}
```

**تنسيقات التصدير:**
- PDF - لوحة كاملة مع الرسوم
- Excel - بيانات مع إحصائيات
- JSON - نسخ احتياطي
- Print - طباعة مباشرة

## إضافة ميزات جديدة

### إضافة مؤشر أداء جديد
1. تحديث `CONFIG.KPI` في `config.js`
2. إضافة منطق التحقق في `validateKPI()`
3. تحديث واجهة النموذج في `index.html`
4. إضافة معالجة في `DataManager`

### إضافة نوع رسم بياني جديد
1. إنشاء دالة في `ChartsManager`
2. إضافة canvas في HTML
3. ربط البيانات والتحديث
4. إضافة خيارات التخصيص

### إضافة دور مستخدم جديد
1. تحديث `CONFIG.USERS.ROLES`
2. إضافة منطق الصلاحيات في `AuthManager`
3. تحديث واجهة إدارة المستخدمين
4. تطبيق الصلاحيات في الواجهة

## التخزين المحلي

### بنية البيانات
```javascript
// المستخدمين
{
    id: string,
    username: string,
    password: string, // مشفرة في الإنتاج
    role: string,
    name: string,
    department: string,
    createdAt: string,
    isActive: boolean
}

// المؤشرات
{
    id: string,
    name: string,
    definition: string,
    target: number,
    actual: number,
    percentage: number,
    status: string,
    period: string,
    department: string,
    notes: string,
    createdAt: string,
    updatedAt: string
}
```

### مفاتيح التخزين
- `kpi_users` - بيانات المستخدمين
- `kpi_data` - بيانات المؤشرات
- `current_user` - المستخدم الحالي
- `user_preferences` - تفضيلات المستخدم
- `app_config` - إعدادات التطبيق

## الأمان والصلاحيات

### الأدوار والصلاحيات
| الدور | عرض | تعديل | حذف | إدارة المستخدمين | استيراد | تصدير |
|-------|------|-------|------|------------------|---------|--------|
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Manager | ✅ | ✅* | ✅* | ❌ | ❌ | ✅ |
| Viewer | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

*مقيد بالقسم المخصص

### اعتبارات الأمان
- تشفير كلمات المرور (مطلوب في الإنتاج)
- انتهاء صلاحية الجلسة
- التحقق من الصلاحيات في كل عملية
- تنظيف المدخلات من XSS

## الأداء والتحسين

### تحسينات الأداء
- Debouncing للبحث (300ms)
- Throttling للتمرير (100ms)
- Lazy loading للرسوم البيانية
- تحديث تلقائي ذكي

### إدارة الذاكرة
- تدمير الرسوم البيانية عند عدم الحاجة
- تنظيف event listeners
- إدارة DOM بكفاءة

## اختبار النظام

### اختبارات وظيفية
1. تسجيل الدخول بالأدوار المختلفة
2. إضافة وتعديل وحذف المؤشرات
3. استيراد ملف Excel
4. تصدير التقارير
5. تطبيق الفلاتر والبحث

### اختبارات الأداء
1. تحميل كمية كبيرة من البيانات
2. التنقل السريع بين التبويبات
3. تحديث الرسوم البيانية
4. عمليات التصدير

### اختبارات التوافق
- متصفحات مختلفة (Chrome, Firefox, Safari, Edge)
- أجهزة مختلفة (Desktop, Tablet, Mobile)
- أنظمة تشغيل مختلفة

## استكشاف الأخطاء

### أخطاء شائعة
1. **الرسوم البيانية لا تظهر**
   - تحقق من تحميل Chart.js
   - تأكد من وجود canvas elements
   - راجع console للأخطاء

2. **فشل استيراد Excel**
   - تحقق من تنسيق الملف
   - تأكد من وجود الأعمدة المطلوبة
   - راجع أسماء الأعمدة

3. **مشاكل الصلاحيات**
   - تحقق من دور المستخدم
   - راجع منطق الصلاحيات
   - تأكد من تطبيق CSS classes

### أدوات التطوير
- استخدم Developer Tools للتتبع
- فعل `DEBUG_MODE` في التكوين
- راجع localStorage للبيانات
- استخدم Network tab لتتبع الطلبات

## التطوير المستقبلي

### ميزات مقترحة
- دعم قواعد البيانات الخارجية
- إشعارات في الوقت الفعلي
- تقارير متقدمة مع AI
- تكامل مع أنظمة خارجية
- تطبيق جوال

### تحسينات تقنية
- استخدام TypeScript
- إضافة Unit Tests
- تحسين الأمان
- دعم PWA
- تحسين الأداء

## المساهمة في التطوير

### إرشادات الكود
- استخدم التعليقات بالعربية والإنجليزية
- اتبع معايير ES6+
- حافظ على بنية الملفات
- اختبر جميع التغييرات

### عملية التطوير
1. Fork المشروع
2. إنشاء branch جديد
3. تطوير الميزة
4. اختبار شامل
5. إرسال Pull Request

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع README.md للاستخدام الأساسي
- تحقق من console للأخطاء
- راجع هذا الدليل للتطوير المتقدم
