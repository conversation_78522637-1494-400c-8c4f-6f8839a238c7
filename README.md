# نظام لوحة تحكم مؤشرات الأداء الرئيسية (KPI Dashboard)

## نظرة عامة
نظام تفاعلي شامل لإدارة وعرض مؤشرات الأداء الرئيسية (KPIs) باستخدام HTML/CSS/JavaScript فقط، مع دعم كامل للغة العربية و RTL.

## الميزات الرئيسية

### 🔐 إدارة المستخدمين والصلاحيات
- نظام تسجيل دخول آمن
- ثلاثة أدوار: مدير النظام، مدير القسم، قارئ
- صلاحيات مختلفة لكل دور
- تخزين آمن في LocalStorage

### 📊 لوحة المؤشرات التفاعلية
- عرض مؤشرات الأداء في بطاقات تفاعلية
- رسوم بيانية متنوعة (Bar, Pie, Radar, Line)
- تلوين شرطي للنسب (أخضر ≥90%, أصفر 70-89%, أح<PERSON><PERSON> <70%)
- فلاتر للفترة الزمنية والقسم

### 📈 المؤشرات المدعومة
1. الإنجاز مقابل الخطة
2. جودة المخرجات
3. كفاءة استخدام الموارد
4. زمن الإنجاز
5. رضا العملاء
6. التحسين المستمر

### 🗃️ إدارة البيانات
- عمليات CRUD كاملة للمؤشرات
- استيراد من ملفات Excel
- تخزين محلي مع IndexedDB/LocalStorage
- التحقق من صحة البيانات

### 📋 الجدول التحليلي
- عرض تفصيلي لجميع المؤشرات
- بحث وتصفية متقدمة
- ترتيب حسب الأعمدة
- تصدير إلى Excel

### 📄 التصدير والتقارير
- تصدير اللوحة كاملة إلى PDF
- تصدير البيانات إلى Excel
- تقارير مخصصة حسب القسم والفترة
- طباعة التقارير

## كيفية التشغيل

### 1. التشغيل المحلي
```bash
# افتح الملف في أي خادم ويب محلي
# مثال باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx http-server

# ثم افتح المتصفح على
http://localhost:8000
```

### 2. التشغيل المباشر
- افتح ملف `index.html` مباشرة في المتصفح
- جميع الميزات تعمل بدون خادم

## بيانات تسجيل الدخول التجريبية

### مدير النظام
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحيات:** جميع الصلاحيات

### مدير القسم
- **اسم المستخدم:** manager
- **كلمة المرور:** manager123
- **الصلاحيات:** إدارة مؤشرات القسم فقط

### قارئ
- **اسم المستخدم:** viewer
- **كلمة المرور:** viewer123
- **الصلاحيات:** عرض فقط

## هيكل المشروع

```
KPI Dashboard/
├── index.html              # الملف الرئيسي
├── css/
│   └── styles.css         # ملف الأنماط
├── js/
│   ├── utils.js           # الوظائف المساعدة
│   ├── auth.js            # إدارة المصادقة
│   ├── data.js            # إدارة البيانات
│   ├── charts.js          # الرسوم البيانية
│   ├── export.js          # التصدير
│   └── app.js             # التطبيق الرئيسي
├── Data.xlsx              # ملف بيانات تجريبي
└── README.md              # دليل الاستخدام
```

## المكتبات المستخدمة

- **Chart.js** - الرسوم البيانية
- **SheetJS** - قراءة وكتابة Excel
- **jsPDF** - تصدير PDF
- **html2canvas** - التقاط لقطات الشاشة
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخط العربي

## الاستخدام

### 1. تسجيل الدخول
- استخدم أحد الحسابات التجريبية المذكورة أعلاه
- سيتم توجيهك إلى لوحة المؤشرات

### 2. عرض المؤشرات
- استخدم الفلاتر لتصفية البيانات
- اعرض الرسوم البيانية التفاعلية
- راجع الجدول التحليلي

### 3. إدارة البيانات
- أضف مؤشرات جديدة (مدير النظام/القسم)
- عدل المؤشرات الموجودة
- استورد بيانات من Excel

### 4. التصدير
- صدر اللوحة كاملة إلى PDF
- صدر البيانات إلى Excel
- اطبع التقارير

## تخصيص النظام

### إضافة مؤشرات جديدة
1. انتقل إلى تبويب "إدارة البيانات"
2. اضغط "إضافة مؤشر جديد"
3. املأ البيانات المطلوبة
4. احفظ المؤشر

### استيراد بيانات Excel
1. حضر ملف Excel بالأعمدة التالية:
   - المؤشر، التعريف، المستهدف، الفعلي، الفترة، القسم، الملاحظات
2. انتقل إلى "إدارة البيانات"
3. اضغط "استيراد Excel"
4. اختر الملف

### إدارة المستخدمين
- متاح لمدير النظام فقط
- يمكن إضافة وتعديل وحذف المستخدمين
- تحديد الأدوار والأقسام

## الدعم الفني

### متطلبات النظام
- متصفح حديث يدعم ES6+
- JavaScript مفعل
- اتصال بالإنترنت لتحميل المكتبات الخارجية

### استكشاف الأخطاء
- تأكد من تفعيل JavaScript
- تحقق من وحدة تحكم المطور للأخطاء
- امسح ذاكرة التخزين المحلي إذا لزم الأمر

## الترخيص
هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## المطور
تم تطوير هذا النظام بواسطة Augment Agent لتوفير حل شامل لإدارة مؤشرات الأداء.
