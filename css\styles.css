/* ===== CSS Variables ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
}

.hidden {
    display: none !important;
}

/* ===== Buttons ===== */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
    font-size: 14px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

/* ===== Modal Styles ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-primary);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
}

/* ===== Header ===== */
.header {
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    padding: 15px 0;
    margin-bottom: 20px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 32px;
    color: var(--primary-color);
}

.logo h1 {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 700;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* ===== Navigation ===== */
.nav-tabs {
    max-width: 1200px;
    margin: 0 auto 30px;
    padding: 0 20px;
    display: flex;
    gap: 10px;
    border-bottom: 1px solid var(--border-color);
}

.nav-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-tab:hover {
    color: var(--primary-color);
    background-color: var(--bg-tertiary);
}

.nav-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* ===== Tab Content ===== */
.tab-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: none;
}

.tab-content.active {
    display: block;
}

/* ===== Filters Section ===== */
.filters-section {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 30px;
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-primary);
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    min-width: 150px;
}

/* ===== KPI Grid ===== */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border-right: 4px solid var(--primary-color);
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.kpi-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.kpi-status {
    font-size: 20px;
}

.kpi-values {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.kpi-value {
    text-align: center;
}

.kpi-value-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.kpi-value-number {
    font-size: 24px;
    font-weight: 700;
}

.kpi-percentage {
    text-align: center;
    margin-bottom: 10px;
}

.percentage-value {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 5px;
}

.percentage-label {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Status Colors */
.status-excellent {
    color: var(--success-color);
}

.status-good {
    color: var(--warning-color);
}

.status-poor {
    color: var(--danger-color);
}

/* ===== Charts Section ===== */
.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: var(--text-primary);
    text-align: center;
}

/* ===== Table Styles ===== */
.table-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.table-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.table-actions input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    min-width: 200px;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
}

tr:hover {
    background-color: var(--bg-tertiary);
}

/* ===== Form Styles ===== */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* ===== Demo Users Info ===== */
.demo-users {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border-right: 4px solid var(--info-color);
}

.demo-users h4 {
    color: var(--info-color);
    margin-bottom: 10px;
}

.demo-users p {
    margin-bottom: 5px;
    font-size: 14px;
}

/* ===== Loading Spinner ===== */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Toast Notifications ===== */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    box-shadow: var(--shadow-md);
    animation: slideIn 0.3s ease;
    max-width: 400px;
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--danger-color);
}

.toast.warning {
    background-color: var(--warning-color);
}

.toast.info {
    background-color: var(--info-color);
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== Data Management ===== */
.data-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.data-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* ===== Reports Section ===== */
.reports-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.report-card {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.report-card h3 {
    margin-bottom: 10px;
    color: var(--text-primary);
}

.report-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* ===== Users Section ===== */
.users-section {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.users-table {
    margin-top: 20px;
}

/* ===== Permission-based visibility ===== */
.admin-only {
    display: none;
}

.admin-manager-only {
    display: none;
}

body.role-admin .admin-only,
body.role-admin .admin-manager-only {
    display: initial;
}

body.role-manager .admin-manager-only {
    display: initial;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .kpi-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .table-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: center;
    }
}

/* ===== Utility Classes ===== */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.p-20 {
    padding: 20px;
}

/* ===== KPI Card Enhancements ===== */
.kpi-card {
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--info-color));
}

.kpi-info {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

/* ===== Enhanced Table Styles ===== */
.table-container {
    max-height: 600px;
    overflow-y: auto;
}

.table-container::-webkit-scrollbar {
    width: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

.table-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* ===== Action Buttons ===== */
.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    margin: 0 2px;
    min-width: auto;
}

.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

/* ===== Status Indicators ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.status-indicator.excellent {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-indicator.good {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-indicator.poor {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* ===== Enhanced Charts ===== */
.chart-container {
    position: relative;
    height: 300px;
}

.chart-container canvas {
    max-height: 250px;
}

/* ===== Progress Bars ===== */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.excellent {
    background-color: var(--success-color);
}

.progress-fill.good {
    background-color: var(--warning-color);
}

.progress-fill.poor {
    background-color: var(--danger-color);
}

/* ===== Enhanced Modal ===== */
.modal-content {
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== Dropdown Enhancements ===== */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: var(--bg-primary);
    min-width: 160px;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    z-index: 1;
    right: 0;
    top: 100%;
}

.dropdown-content a {
    color: var(--text-primary);
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    transition: var(--transition);
}

.dropdown-content a:hover {
    background-color: var(--bg-tertiary);
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* ===== Card Hover Effects ===== */
.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}

/* ===== Print Styles ===== */
@media print {
    .nav-tabs,
    .filters-section,
    .table-actions,
    .btn,
    .modal {
        display: none !important;
    }

    .tab-content {
        display: block !important;
    }

    .kpi-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .chart-container {
        page-break-inside: avoid;
    }
}

/* ===== KPI Details Modal ===== */
.kpi-details {
    max-width: 600px;
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
}

.detail-item {
    padding: 10px;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-primary);
    display: block;
    margin-bottom: 5px;
}

.detail-item p {
    margin: 0;
    color: var(--text-secondary);
}

/* ===== Quick Stats Modal ===== */
.quick-stats {
    max-width: 700px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    border-radius: var(--border-radius);
    color: white;
}

.stat-card.excellent {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-card.good {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-card.poor {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.stat-card.total {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
}

.performance-summary {
    background-color: var(--bg-tertiary);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.performance-summary h4 {
    margin-bottom: 15px;
    color: var(--text-primary);
}

.performance-summary p {
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.recommendations {
    background-color: var(--bg-tertiary);
    padding: 20px;
    border-radius: var(--border-radius);
}

.recommendations h4 {
    margin-bottom: 15px;
    color: var(--text-primary);
}

.recommendation {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    border-right: 4px solid;
}

.recommendation.critical {
    background-color: rgba(239, 68, 68, 0.1);
    border-right-color: var(--danger-color);
}

.recommendation.department {
    background-color: rgba(245, 158, 11, 0.1);
    border-right-color: var(--warning-color);
}

.recommendation.kpis {
    background-color: rgba(6, 182, 212, 0.1);
    border-right-color: var(--info-color);
}

.recommendation strong {
    color: var(--text-primary);
    display: block;
    margin-bottom: 5px;
}

.recommendation p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
}

/* ===== Advanced Filters ===== */
.advanced-filters {
    max-width: 600px;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

/* ===== Enhanced Table Cells ===== */
.kpi-name-cell {
    max-width: 200px;
}

.kpi-name-cell strong {
    color: var(--text-primary);
}

.kpi-name-cell small {
    color: var(--text-muted);
    font-size: 11px;
    line-height: 1.3;
}

.percentage-cell {
    min-width: 100px;
}

.percentage-cell .progress-bar {
    margin-top: 5px;
    height: 4px;
}

.notes-cell {
    max-width: 200px;
    word-wrap: break-word;
    font-size: 13px;
    line-height: 1.4;
}

/* ===== Sortable Table Headers ===== */
.sortable-header {
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
    position: relative;
    padding-left: 20px;
}

.sortable-header:hover {
    background-color: var(--bg-secondary);
}

.sortable-header::after {
    content: '↕️';
    position: absolute;
    left: 5px;
    opacity: 0.5;
}

.sortable-header.asc::after {
    content: '↑';
    opacity: 1;
}

.sortable-header.desc::after {
    content: '↓';
    opacity: 1;
}

/* ===== Mobile Responsive Enhancements ===== */
@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    .kpi-name-cell,
    .notes-cell {
        max-width: none;
    }

    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 11px;
    }
}

/* ===== Table Toolbar ===== */
.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.record-count {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 600;
}

.record-count span {
    color: var(--primary-color);
    font-weight: 700;
}

/* ===== Enhanced Animations ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.kpi-card {
    animation: fadeIn 0.5s ease forwards;
}

.kpi-card:nth-child(1) { animation-delay: 0.1s; }
.kpi-card:nth-child(2) { animation-delay: 0.2s; }
.kpi-card:nth-child(3) { animation-delay: 0.3s; }
.kpi-card:nth-child(4) { animation-delay: 0.4s; }
.kpi-card:nth-child(5) { animation-delay: 0.5s; }
.kpi-card:nth-child(6) { animation-delay: 0.6s; }

/* ===== Loading States ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-card {
    height: 200px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.skeleton-text {
    height: 20px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

/* ===== Error States ===== */
.error-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.error-state i {
    font-size: 48px;
    color: var(--danger-color);
    margin-bottom: 20px;
}

.error-state h3 {
    margin-bottom: 10px;
    color: var(--text-primary);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    color: var(--text-muted);
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--text-primary);
}

/* ===== Accessibility Improvements ===== */
.btn:focus,
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== Dark Mode Support (Optional) ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1e293b;
        --bg-secondary: #0f172a;
        --bg-tertiary: #334155;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #64748b;
        --border-color: #475569;
    }

    .modal {
        background-color: rgba(0, 0, 0, 0.8);
    }

    .loading-skeleton {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    }
}

/* ===== Mobile Responsive for Sidebar ===== */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 80px;
        right: -280px;
        height: calc(100vh - 80px);
        z-index: 1000;
        transition: right 0.3s ease;
    }

    .sidebar.open {
        right: 0;
    }

    .sidebar.collapsed {
        width: 280px;
        right: -280px;
    }

    .main-content {
        width: 100%;
        margin-right: 0;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }

    .sidebar-overlay.active {
        display: block;
    }

    .chart-row {
        grid-template-columns: 1fr;
    }

    .overview-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ===== Analysis Report Styles ===== */
.analysis-report {
    max-width: 700px;
}

.analysis-section {
    margin-bottom: 25px;
    padding: 20px;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.analysis-section h4 {
    margin-bottom: 15px;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.analysis-section ul {
    margin: 0;
    padding-right: 20px;
}

.analysis-section li {
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.trend-analysis {
    max-width: 500px;
    text-align: center;
}

.coming-soon {
    margin-top: 30px;
    padding: 30px;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
}

.coming-soon i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* ===== Enhanced Sidebar Animations ===== */
@keyframes sidebarSlideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.sidebar {
    animation: sidebarSlideIn 0.3s ease;
}

.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    right: 100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: right 0.5s ease;
}

.nav-link:hover::after {
    right: -100%;
}

/* ===== Tooltip for Collapsed Sidebar ===== */
.sidebar.collapsed .nav-link {
    position: relative;
}

.sidebar.collapsed .nav-link:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 70px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--text-primary);
    color: var(--bg-primary);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

/* ===== Settings Enhancements ===== */
.setting-item input[type="file"] {
    width: 100%;
    padding: 10px;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.setting-item input[type="file"]:hover {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

/* ===== Breadcrumb Navigation ===== */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb-item {
    color: var(--text-secondary);
    font-size: 14px;
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 600;
}

.breadcrumb-separator {
    color: var(--text-muted);
}

/* ===== Enhanced Sidebar Interactions ===== */
.sidebar .nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

.sidebar-header .sidebar-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== Smooth Transitions ===== */
.tab-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.tab-content.active {
    opacity: 1;
    transform: translateY(0);
}

/* ===== Enhanced User Profile ===== */
.user-profile:hover {
    background-color: rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.user-profile:hover .user-avatar {
    transform: scale(1.05);
}

/* ===== Sidebar Responsive Enhancements ===== */
@media (max-width: 1024px) {
    .sidebar {
        width: 250px;
    }

    .sidebar.collapsed {
        width: 60px;
    }
}

@media (max-width: 768px) {
    .main-layout {
        position: relative;
    }

    .sidebar {
        position: fixed;
        top: 80px;
        right: -280px;
        height: calc(100vh - 80px);
        z-index: 1000;
        width: 280px !important;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    }

    .sidebar.open {
        right: 0;
    }

    .sidebar.collapsed {
        right: -280px;
    }

    .main-content {
        width: 100%;
        margin-right: 0;
        padding: 20px;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .sidebar-overlay.active {
        display: block;
        opacity: 1;
    }
}

/* ===== Dark Mode Styles ===== */
.dark-mode {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #475569;
}

.dark-mode .sidebar {
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.dark-mode .nav-link:hover {
    background-color: rgba(37, 99, 235, 0.2);
}

.dark-mode .user-profile:hover {
    background-color: rgba(37, 99, 235, 0.2);
}

/* ===== Loading States for Sidebar ===== */
.sidebar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.sidebar-loading i {
    font-size: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== Sidebar Badge System ===== */
.nav-link .badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.sidebar.collapsed .nav-link .badge {
    top: 5px;
    left: 5px;
}

/* ===== Sidebar Styles ===== */
.main-layout {
    display: flex;
    min-height: calc(100vh - 80px);
}

.sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--bg-tertiary);
}

.sidebar-title {
    font-weight: 600;
    color: var(--text-primary);
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-title {
    opacity: 0;
    pointer-events: none;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: right;
    border-radius: 0 25px 25px 0;
    margin-left: 10px;
    position: relative;
    font-family: inherit;
}

.nav-link:hover {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    transform: translateX(-5px);
}

.nav-link.active {
    background: linear-gradient(90deg, var(--primary-color), rgba(37, 99, 235, 0.8));
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid var(--primary-color);
}

.nav-link i {
    font-size: 18px;
    min-width: 20px;
    text-align: center;
}

.nav-text {
    transition: opacity 0.3s ease;
    font-weight: 500;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    pointer-events: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 15px 10px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.user-details {
    flex: 1;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .user-details {
    opacity: 0;
    pointer-events: none;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 2px;
}

.user-role {
    font-size: 12px;
    color: var(--text-secondary);
}

.main-content {
    flex: 1;
    padding: 30px;
    background-color: var(--bg-primary);
    overflow-y: auto;
    transition: margin-right 0.3s ease;
}

/* ===== Analytics Tab Styles ===== */
.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.analytics-actions {
    display: flex;
    gap: 10px;
}

.performance-overview {
    margin-bottom: 30px;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: var(--bg-secondary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.card-icon.excellent {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.card-icon.good {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.card-icon.poor {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.card-icon.total {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
}

.card-content h3 {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: var(--text-primary);
}

.card-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.advanced-charts {
    margin-top: 30px;
}

.chart-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* ===== Settings Tab Styles ===== */
.settings-section {
    max-width: 800px;
}

.settings-card {
    background: var(--bg-secondary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 25px;
}

.settings-card h3 {
    margin-bottom: 20px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.setting-item {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
}

.setting-item input[type="checkbox"] {
    margin-left: 8px;
    transform: scale(1.2);
}

.setting-item select {
    width: 100%;
    max-width: 300px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.setting-item small {
    display: block;
    margin-top: 5px;
    color: var(--text-muted);
    font-size: 12px;
}

.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}
