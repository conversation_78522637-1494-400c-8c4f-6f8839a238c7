<!DOCTYPE html>
<html>
<head>
    <title>إنشاء ملف Excel تجريبي</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>إنشاء ملف Excel تجريبي</h1>
    <button onclick="createSampleExcel()">إنشاء ملف Data.xlsx</button>

    <script>
        function createSampleExcel() {
            const sampleData = [
                {
                    'المؤشر': 'الإنجاز مقابل الخطة',
                    'التعريف': 'نسبة الإنجاز الفعلي مقارنة بالخطة المحددة',
                    'المستهدف': 100,
                    'الفعلي': 95,
                    'الفترة': '2024-Q3',
                    'القسم': 'العمليات',
                    'الملاحظات': 'أداء جيد مع تحسن ملحوظ'
                },
                {
                    'المؤشر': 'جودة المخرجات',
                    'التعريف': 'مؤشر جودة المنتجات والخدمات المقدمة',
                    'المستهدف': 95,
                    'الفعلي': 88,
                    'الفترة': '2024-Q3',
                    'القسم': 'العمليات',
                    'الملاحظات': 'يحتاج إلى تحسين في بعض الجوانب'
                },
                {
                    'المؤشر': 'كفاءة استخدام الموارد',
                    'التعريف': 'مدى فعالية استخدام الموارد المتاحة',
                    'المستهدف': 85,
                    'الفعلي': 92,
                    'الفترة': '2024-Q3',
                    'القسم': 'المالية',
                    'الملاحظات': 'أداء ممتاز في إدارة الموارد'
                },
                {
                    'المؤشر': 'زمن الإنجاز',
                    'التعريف': 'متوسط الوقت المطلوب لإنجاز المهام',
                    'المستهدف': 24,
                    'الفعلي': 20,
                    'الفترة': '2024-Q3',
                    'القسم': 'العمليات',
                    'الملاحظات': 'تحسن كبير في سرعة الإنجاز'
                },
                {
                    'المؤشر': 'رضا العملاء',
                    'التعريف': 'مؤشر رضا العملاء عن الخدمات المقدمة',
                    'المستهدف': 90,
                    'الفعلي': 87,
                    'الفترة': '2024-Q3',
                    'القسم': 'المبيعات',
                    'الملاحظات': 'رضا جيد مع إمكانية للتحسين'
                },
                {
                    'المؤشر': 'التحسين المستمر',
                    'التعريف': 'عدد مبادرات التحسين المنفذة',
                    'المستهدف': 10,
                    'الفعلي': 12,
                    'الفترة': '2024-Q3',
                    'القسم': 'الموارد البشرية',
                    'الملاحظات': 'تجاوز المستهدف بنجاح'
                },
                {
                    'المؤشر': 'معدل النمو',
                    'التعريف': 'معدل نمو الإيرادات الشهرية',
                    'المستهدف': 15,
                    'الفعلي': 18,
                    'الفترة': '2024-Q4',
                    'القسم': 'المبيعات',
                    'الملاحظات': 'نمو ممتاز يفوق التوقعات'
                },
                {
                    'المؤشر': 'كفاءة التسويق',
                    'التعريف': 'عائد الاستثمار في الحملات التسويقية',
                    'المستهدف': 300,
                    'الفعلي': 280,
                    'الفترة': '2024-Q4',
                    'القسم': 'التسويق',
                    'الملاحظات': 'أداء جيد مع إمكانية للتحسين'
                }
            ];

            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(sampleData);
            
            // تحديد عرض الأعمدة
            ws['!cols'] = [
                { wch: 25 }, // المؤشر
                { wch: 40 }, // التعريف
                { wch: 12 }, // المستهدف
                { wch: 12 }, // الفعلي
                { wch: 15 }, // الفترة
                { wch: 15 }, // القسم
                { wch: 30 }  // الملاحظات
            ];
            
            XLSX.utils.book_append_sheet(wb, ws, 'KPI Data');
            XLSX.writeFile(wb, 'Data.xlsx');
            
            alert('تم إنشاء ملف Data.xlsx بنجاح!');
        }
    </script>
</body>
</html>
